//@version=5
indicator("Minervini VCP 趨勢策略", overlay=true)

// 輸入參數
vol_length = input.int(20, title="成交量均線長度")
vol_mult = input.float(2.0, title="高成交量倍數")
vol_dryup_mult = input.float(0.5, title="成交量枯竭倍數")
dry_up_bars = input.int(3, title="連續枯竭K線數", group="成交量")

len_ma_short = input.int(10, title="短期均線", group="均線")
len_ma_medium_short = input.int(20, title="中短期均線", group="均線")
len_ma_fast = input.int(50, title="快線", group="均線")
len_ma_medium = input.int(150, title="中線", group="均線")
len_ma_slow = input.int(200, title="慢線", group="均線")

atr_len_short = input.int(10, title="短期ATR長度", group="VCP")
atr_len_long = input.int(50, title="長期ATR長度", group="VCP")
atr_contraction_ratio = input.float(0.6, title="波動收縮比例", group="VCP")
min_vcp_contractions = input.int(2, title="最小收縮次數", group="VCP")
vcp_lookback = input.int(30, title="VCP回溯週期", group="VCP")
stop_loss_atr_mult = input.float(2.0, title="停損ATR倍數", group="風險管理")
profit_target_atr_mult = input.float(4.0, title="止盈ATR倍數", group="風險管理")
enable_price_action_stop_loss = input.bool(true, title="價格行為停損", group="風險管理")

enable_trailing_stop = input.bool(false, title="追蹤止損", group="風險管理")
trailing_stop_type = input.string("ATR 倍數", title="追蹤止損類型", options=["ATR 倍數", "前一根K線低點", "短期均線"], group="風險管理")
trailing_stop_atr_mult = input.float(1.5, title="追蹤止損ATR倍數", group="風險管理")

// 突破相關
breakout_volume_mult = input.float(3.0, title="突破成交量倍數 (Dan Zanger)", group="突破", tooltip="突破當日成交量應顯著放大，通常是均量的3倍以上") // 提高預設值，更嚴格篩選
breakout_volume_lookback = input.int(50, title="突破成交量回溯週期", group="突破", tooltip="突破當日成交量應為過去N根K線的最高量") // 增加回溯週期，確保是近期最高量
breakout_close_position = input.float(0.8, title="突破收盤價位置 (Dan Zanger)", tooltip="收盤價在K線實體中的相對位置 (0-1)，例如0.8表示收盤價在K線實體上部80%以上，強調強勢收盤", group="突破") // 提高要求，強調強勢收盤
breakout_confirm_bars = input.int(1, title="突破後確認K線數", group="突破", tooltip="突破後下一根K線的表現，例如收盤價是否維持在突破位上方") // 簡化確認，更符合短期交易
retrace_atr_mult = input.float(0.3, title="突破回踩 ATR 倍數", group="突破", tooltip="突破後回踩的深度，以 ATR 倍數衡量，回踩越淺越強勢") // 降低回踩容忍度，強調強勢回踩

// 回調買入相關 (Mark Minervini & Jesse Livermore & Wyckoff 思路)
retrace_ma_length = input.int(20, title="回調均線長度", group="回調買入")
retrace_volume_mult = input.float(0.5, title="回調成交量倍數 (低於均量)", group="回調買入", tooltip="回調時成交量應顯著萎縮，表明賣壓枯竭 (Wyckoff 思路)") // 降低要求，強調成交量枯竭
retrace_price_tolerance_atr_mult = input.float(0.3, title="回調價格容忍度 (ATR 倍數)", group="回調買入", tooltip="價格距離回調均線的最大容忍度，回調越淺越強勢") // 降低容忍度，強調強勢回調

// William O'Neil CANSLIM 相對強度策略
rs_length = input.int(100, title="長期相對強度週期", group="William O'Neil CANSLIM策略")
short_term_rs_length = input.int(20, title="短期相對強度週期", group="William O'Neil CANSLIM策略")
rs_rank_threshold = input.float(80, title="相對強度排名門檻", group="William O'Neil CANSLIM策略")
price_performance_weeks = input.int(12, title="價格表現週數", group="William O'Neil CANSLIM策略")
volume_surge_threshold = input.float(50, title="成交量激增門檻(%)", group="William O'Neil CANSLIM策略")

// Larry Williams %R 策略增強
williams_r_length = input.int(14, title="Williams %R 長度", group="Larry Williams 策略")
williams_r_overbought = input.float(-20, title="Williams %R 超買線", group="Larry Williams 策略")
williams_r_oversold = input.float(-80, title="Williams %R 超賣線", group="Larry Williams 策略")
williams_r_enable_divergence = input.bool(true, title="啟用Williams %R背離檢測", group="Larry Williams 策略")
williams_r_lookback = input.int(20, title="Williams %R背離回溯週期", group="Larry Williams 策略")

// Kristjan Kullamägi Episodic Pivot 策略增強
ep_enable = input.bool(true, title="啟用EP檢測", group="Kristjan Kullamägi EP策略")
ep_gap_pct = input.float(3.0, title="最小跳空幅度 (%)", group="Kristjan Kullamägi EP策略")
ep_vol_mult = input.float(2.5, title="最小成交量倍數", group="Kristjan Kullamägi EP策略")
ep_consolidation_days = input.int(7, title="EP後盤整天數", group="Kristjan Kullamägi EP策略")
ep_min_price = input.float(5.0, title="EP最低價格門檻", group="Kristjan Kullamägi EP策略")
ep_max_price = input.float(500.0, title="EP最高價格門檻", group="Kristjan Kullamägi EP策略")
ep_rs_threshold = input.float(0.7, title="EP相對強度門檻", group="Kristjan Kullamägi EP策略")
ep_breakout_vol_mult = input.float(1.5, title="EP突破成交量倍數", group="Kristjan Kullamägi EP策略")

// --- 改進 2: Linda Raschke 的動量衰竭 (RSI 背離) ---
momentum_div_enable = input.bool(true, title="啟用動量背離檢測 (RSI)", group="動量與背離")
rsi_length = input.int(14, title="RSI 長度", group="動量與背離")
rsi_lookback = input.int(30, title="RSI 背離回溯週期", group="動量與背離")

// Linda Raschke 波動性策略增強
volatility_filter_enable = input.bool(true, title="啟用波動性過濾器", group="Linda Raschke 波動性策略")
bb_length = input.int(20, title="布林帶長度", group="Linda Raschke 波動性策略")
bb_stddev = input.float(2.0, title="布林帶標準差", group="Linda Raschke 波動性策略")
bbw_squeeze_level = input.float(0.05, title="Squeeze閾值", group="Linda Raschke 波動性策略")
keltner_length = input.int(20, title="Keltner通道長度", group="Linda Raschke 波動性策略")
keltner_mult = input.float(1.5, title="Keltner通道倍數", group="Linda Raschke 波動性策略")
volatility_breakout_mult = input.float(1.2, title="波動性突破倍數", group="Linda Raschke 波動性策略")

// Minervini 杯柄形態
cup_length = input.int(50, title="杯部回溯週期 (K線數)", group="杯柄形態")
handle_length = input.int(15, title="柄部回溯週期 (K線數)", group="杯柄形態")
handle_depth_ratio = input.float(0.5, title="柄部回調深度比例 (相對於杯部)", group="杯柄形態")
min_cup_depth_atr = input.float(3.0, title="最小杯部深度 (ATR 倍數)", group="杯柄形態")

// Nicolas Darvas 箱體理論
darvas_box_lookback = input.int(10, title="Darvas 箱體回溯週期", group="Darvas 箱體")
darvas_box_breakout_vol_mult = input.float(1.2, title="Darvas 箱體突破成交量倍數", group="Darvas 箱體")

// Jesse Livermore 關鍵點
pivot_high_lookback = input.int(50, title="關鍵高點回溯週期", group="Livermore 關鍵點")
pivot_low_lookback = input.int(50, title="關鍵低點回溯週期", group="Livermore 關鍵點")

// 多時間框架分析
higher_timeframe = input.string("D", title="更高時間框架 (例如: D, W, M)", group="多時間框架")

// --- 計算指標 ---
// 均線
ma_short = ta.sma(close, len_ma_short)
ma_medium_short = ta.sma(close, len_ma_medium_short)
ma_fast = ta.sma(close, len_ma_fast)
ma_medium = ta.sma(close, len_ma_medium)
ma_slow = ta.sma(close, len_ma_slow)
ma_retrace = ta.sma(close, retrace_ma_length) // 回調均線 (Mark Minervini & Jesse Livermore 思路)

// Jesse Livermore 關鍵點
livermore_pivot_high = ta.highest(high, pivot_high_lookback)
livermore_pivot_low = ta.lowest(low, pivot_low_lookback)

// Nicolas Darvas 箱體
// 尋找 Darvas 箱體高點：過去 darvas_box_lookback 根 K 線的最高價，且該高點之後沒有更高的高點
darvas_box_high = ta.highest(high, darvas_box_lookback)
// 尋找 Darvas 箱體低點：過去 darvas_box_lookback 根 K 線的最低價，且該低點之後沒有更低的低點
darvas_box_low = ta.lowest(low, darvas_box_lookback)

// 成交量移動平均
vol_ma = ta.sma(volume, vol_length)

// 波動性指標 (ATR)
atr_short = ta.atr(atr_len_short)
atr_long = ta.atr(atr_len_long)

// 成交量潮 (On-Balance Volume, OBV)
obv = ta.obv
obv_ma = ta.sma(obv, vol_length)

// OBV 趨勢判斷 (用於輔助 Wyckoff 階段判斷)
is_obv_uptrend = obv > obv_ma and obv_ma > obv_ma[1]
is_obv_downtrend = obv < obv_ma and obv_ma < obv_ma[1]

// William O'Neil CANSLIM 相對強度分析
relative_strength = (close - ta.lowest(low, rs_length)) / (ta.highest(high, rs_length) - ta.lowest(low, rs_length))
short_term_relative_strength = (close - ta.lowest(low, short_term_rs_length)) / (ta.highest(high, short_term_rs_length) - ta.lowest(low, short_term_rs_length))

// O'Neil 價格表現分析
price_change_4weeks = (close - close[20]) / close[20] * 100
price_change_12weeks = (close - close[60]) / close[60] * 100
price_change_52weeks = (close - close[252]) / close[252] * 100

// CANSLIM 成交量分析
avg_volume_50 = ta.sma(volume, 50)
volume_surge = (volume - avg_volume_50) / avg_volume_50 * 100
is_volume_surge = volume_surge > volume_surge_threshold

// O'Neil 相對強度評級 (簡化版)
rs_rating = relative_strength * 100
is_rs_strong = rs_rating > rs_rank_threshold

// CANSLIM 新高檢測
is_new_high_52week = high >= ta.highest(high, 252)
is_near_new_high = close >= ta.highest(high, 252) * 0.85

// Larry Williams %R 增強策略
williams_r = ta.wpr(williams_r_length)
williams_r_smooth = ta.sma(williams_r, 3) // 平滑化減少噪音

// Larry Williams 動量確認 - 多重時間框架
williams_r_fast = ta.wpr(7)  // 快速Williams %R
williams_r_slow = ta.wpr(21) // 慢速Williams %R

// Williams %R 背離檢測 (Larry Williams 高級策略)
williams_r_pivot_high = ta.pivothigh(williams_r, williams_r_lookback, williams_r_lookback)
williams_r_pivot_low = ta.pivotlow(williams_r, williams_r_lookback, williams_r_lookback)

// 看漲背離：價格創新低但Williams %R未創新低
price_makes_lower_low = low < ta.lowest(low, williams_r_lookback)[1]
williams_r_makes_higher_low = williams_r > ta.lowest(williams_r, williams_r_lookback)[1]
williams_r_bullish_divergence = williams_r_enable_divergence and price_makes_lower_low and williams_r_makes_higher_low

// 看跌背離：價格創新高但Williams %R未創新高
price_makes_higher_high_wr = high > ta.highest(high, williams_r_lookback)[1]
williams_r_makes_lower_high = williams_r < ta.highest(williams_r, williams_r_lookback)[1]
williams_r_bearish_divergence = williams_r_enable_divergence and price_makes_higher_high_wr and williams_r_makes_lower_high

// Larry Williams 短期反轉信號
williams_r_oversold_bounce = williams_r[1] < williams_r_oversold and williams_r > williams_r[1] and close > open
williams_r_overbought_drop = williams_r[1] > williams_r_overbought and williams_r < williams_r[1] and close < open

// === 核心支撐阻力位計算函數 ===
get_key_levels() =>
    // 計算關鍵支撐阻力位
    float key_support_1 = ta.lowest(low, 20)  // 20日最低點
    float key_support_2 = ta.lowest(low, 50)  // 50日最低點
    float key_resistance_1 = ta.highest(high, 20)  // 20日最高點
    float key_resistance_2 = ta.highest(high, 50)  // 50日最高點

    // 均線支撐阻力
    float ma_support = math.min(ma_short, math.min(ma_medium_short, ma_fast))
    float ma_resistance = math.max(ma_short, math.max(ma_medium_short, ma_fast))

    // Darvas 箱體關鍵位
    float darvas_support = darvas_box_low
    float darvas_resistance = darvas_box_high

    // Livermore 關鍵點
    float livermore_support = livermore_pivot_low
    float livermore_resistance = livermore_pivot_high

    // 綜合關鍵支撐位（取最接近當前價格的有效支撐）
    float primary_support = na
    float secondary_support = na
    float primary_resistance = na
    float secondary_resistance = na

    // 計算主要支撐位（最接近當前價格且在下方的支撐）
    array<float> support_levels = array.new<float>()
    if not na(key_support_1) and key_support_1 < close
        array.push(support_levels, key_support_1)
    if not na(ma_support) and ma_support < close
        array.push(support_levels, ma_support)
    if not na(darvas_support) and darvas_support < close
        array.push(support_levels, darvas_support)
    if not na(livermore_support) and livermore_support < close
        array.push(support_levels, livermore_support)

    // 排序並選擇最接近的支撐位
    if array.size(support_levels) > 0
        array.sort(support_levels, order.descending)
        primary_support := array.get(support_levels, 0)
        if array.size(support_levels) > 1
            secondary_support := array.get(support_levels, 1)

    // 計算主要阻力位（最接近當前價格且在上方的阻力）
    array<float> resistance_levels = array.new<float>()
    if not na(key_resistance_1) and key_resistance_1 > close
        array.push(resistance_levels, key_resistance_1)
    if not na(ma_resistance) and ma_resistance > close
        array.push(resistance_levels, ma_resistance)
    if not na(darvas_resistance) and darvas_resistance > close
        array.push(resistance_levels, darvas_resistance)
    if not na(livermore_resistance) and livermore_resistance > close
        array.push(resistance_levels, livermore_resistance)

    // 排序並選擇最接近的阻力位
    if array.size(resistance_levels) > 0
        array.sort(resistance_levels, order.ascending)
        primary_resistance := array.get(resistance_levels, 0)
        if array.size(resistance_levels) > 1
            secondary_resistance := array.get(resistance_levels, 1)

    [primary_support, secondary_support, primary_resistance, secondary_resistance]

// === 市場階段量化評分函數 ===
get_market_stage_score(is_minervini_uptrend, is_short_term_uptrend, is_minervini_downtrend, is_short_term_downtrend, is_htf_minervini_uptrend, is_htf_minervini_downtrend, is_volume_surge, volume_dry_up, high_volume, is_demand_coming_in, relative_strength, williams_r, is_bearish_divergence, is_vcp_pattern, is_cup_with_handle, is_episodic_pivot, is_bb_squeeze) =>
    float trend_score = 0.0
    float volume_score = 0.0
    float momentum_score = 0.0
    float structure_score = 0.0

    // 趨勢評分 (0-25分)
    if is_minervini_uptrend
        trend_score += 15.0
    else if is_short_term_uptrend
        trend_score += 10.0
    else if is_minervini_downtrend
        trend_score -= 15.0
    else if is_short_term_downtrend
        trend_score -= 10.0

    if is_htf_minervini_uptrend
        trend_score += 10.0
    else if is_htf_minervini_downtrend
        trend_score -= 10.0

    // 成交量評分 (0-25分)
    if is_volume_surge
        volume_score += 10.0
    if volume_dry_up
        volume_score += 5.0
    if high_volume
        volume_score += 5.0
    if is_demand_coming_in
        volume_score += 5.0

    // 動量評分 (0-25分)
    if relative_strength > 0.8
        momentum_score += 15.0
    else if relative_strength > 0.6
        momentum_score += 10.0
    else if relative_strength < 0.3
        momentum_score -= 10.0

    if williams_r > -20
        momentum_score -= 5.0
    else if williams_r < -80
        momentum_score += 5.0

    if not is_bearish_divergence
        momentum_score += 5.0
    else
        momentum_score -= 10.0

    // 結構評分 (0-25分)
    if is_vcp_pattern
        structure_score += 10.0
    if is_cup_with_handle
        structure_score += 8.0
    if is_episodic_pivot
        structure_score += 7.0
    if is_bb_squeeze
        structure_score += 5.0

    float total_score = trend_score + volume_score + momentum_score + structure_score
    [total_score, trend_score, volume_score, momentum_score, structure_score]

// 風險管理文字生成函數
get_risk_management_text(bool is_long_trade, float entry_price, float atr_value, float stop_loss_atr_mult, float profit_target_atr_mult, bool enable_price_action_stop_loss, float price_action_stop_loss_level, bool enable_trailing_stop, string trailing_stop_type, int len_ma_short, float lowest_in_vcp_lookback, float livermore_pivot_low_val, float livermore_pivot_high_val, float darvas_box_high_val, float darvas_box_low_val) =>
    string resultText = ""
    float sl_price = na
    float tp_price = na

    if is_long_trade
        if enable_price_action_stop_loss
            sl_price := math.min(price_action_stop_loss_level, lowest_in_vcp_lookback)
            sl_price := math.min(sl_price, livermore_pivot_low_val)
            sl_price := math.min(sl_price, darvas_box_high_val - atr_value * 0.5) // Darvas 箱體突破的止損
            resultText += "\n> 建議止損價 (價格行為): " + str.tostring(sl_price, "#.##") + " (基於突破 K 線低點/關鍵支撐/VCP 底部/Darvas 箱體高點下方)。\n這符合 Minervini 的『保護資本』原則，並利用 Livermore 的關鍵點概念來設置防禦性止損。"
        else
            sl_price := entry_price - atr_value * stop_loss_atr_mult
            resultText += "\n> 建議止損價 (ATR): " + str.tostring(sl_price, "#.##") + "。"

        tp_price := entry_price + atr_value * profit_target_atr_mult
        resultText += "\n> 建議止盈價: " + str.tostring(tp_price, "#.##") + " (基於 Mark Minervini 的目標價位概念)。"
        resultText += "\n> 止盈目標設定為入場價位加上 " + str.tostring(profit_target_atr_mult) + " 倍 ATR。\n此外，當出現 Wyckoff 派發信號 (如買入高潮、UTAD、無需求測試) 或假突破信號時，應考慮反轉提示，及時止盈離場 (Jesse Livermore 思路)。"

        if enable_trailing_stop
            if trailing_stop_type == "ATR 倍數"
                resultText += "\n> 啟用追蹤止損: 建議追蹤止損價為最高價減去 " + str.tostring(trailing_stop_atr_mult, "#.##") + " 倍 ATR。\n這有助於在趨勢中保護已實現的利潤，符合短期交易者保護資本和利潤的原則。"
            else if trailing_stop_type == "前一根K線低點"
                resultText += "\n> 啟用追蹤止損: 建議追蹤止損價為前一根 K 線的低點。\n這有助於在趨勢中保護已實現的利潤，並利用價格行為來動態調整止損，符合短期交易者保護資本和利潤的原則。"
            else if trailing_stop_type == "短期均線"
                resultText += "\n> 啟用追蹤止損: 建議追蹤止損價為短期均線 (例如 " + str.tostring(len_ma_short) + " 日均線) 下方。\n這有助於在趨勢中保護已實現的利潤，並利用均線作為動態支撐，符合短期交易者保護資本和利潤的原則。"
    else // Short trade
        if enable_price_action_stop_loss
            sl_price := math.max(price_action_stop_loss_level, livermore_pivot_high_val)
            sl_price := math.max(sl_price, darvas_box_low_val + atr_value * 0.5) // Darvas 箱體跌破的止損
            resultText += "\n> 建議止損價 (價格行為): " + str.tostring(sl_price, "#.##") + " (基於跌破 K 線高點/關鍵阻力/Darvas 箱體低點上方)。\n這符合 Minervini 的『保護資本』原則，並利用 Livermore 的關鍵點概念來設置防禦性止損。"
        else
            sl_price := entry_price + atr_value * stop_loss_atr_mult
            resultText += "\n> 建議止損價 (ATR): " + str.tostring(sl_price, "#.##") + "。"

        tp_price := entry_price - atr_value * profit_target_atr_mult
        resultText += "\n> 建議止盈價: " + str.tostring(tp_price, "#.##") + " (基於 Mark Minervini 的目標價位概念)。"
        resultText += "\n> 止盈目標設定為入場價位減去 " + str.tostring(profit_target_atr_mult) + " 倍 ATR。\n此外，當出現 Wyckoff 累積信號 (如賣出高潮、Spring、需求進入測試) 或假跌破信號時，應考慮反轉提示，及時止盈離場 (Jesse Livermore 思路)。"

        if enable_trailing_stop
            if trailing_stop_type == "ATR 倍數"
                resultText += "\n> 啟用追蹤止損: 建議追蹤止損價為最低價加上 " + str.tostring(trailing_stop_atr_mult, "#.##") + " 倍 ATR。\n這有助於在趨勢中保護已實現的利潤，符合短期交易者保護資本和利潤的原則。"
            else if trailing_stop_type == "前一根K線高點"
                resultText += "\n> 啟用追蹤止損: 建議追蹤止損價為前一根 K 線的高點。\n這有助於在趨勢中保護已實現的利潤，並利用價格行為來動態調整止損，符合短期交易者保護資本和利潤的原則。"
            else if trailing_stop_type == "短期均線"
                resultText += "\n> 啟用追蹤止損: 建議追蹤止損價為短期均線 (例如 " + str.tostring(len_ma_short) + " 日均線) 上方。\n這有助於在趨勢中保護已實現的利潤，並利用均線作為動態阻力，符合短期交易者保護資本和利潤的原則。"
    resultText

// === 增強風險管理文字生成函數 ===
get_enhanced_risk_management_text(bool is_long_trade, float entry_price, float atr_value, float stop_loss_atr_mult, float profit_target_atr_mult, bool enable_price_action_stop_loss, float price_action_stop_loss_level, bool enable_trailing_stop, string trailing_stop_type, int len_ma_short, float lowest_in_vcp_lookback, float livermore_pivot_low_val, float livermore_pivot_high_val, float darvas_box_high_val, float darvas_box_low_val, float primary_support, float secondary_support, float primary_resistance, float secondary_resistance) =>
    string resultText = ""
    float sl_price = na
    float tp_price = na
    float risk_reward_ratio = na

    if is_long_trade
        // 計算最優止損位
        if enable_price_action_stop_loss
            sl_price := price_action_stop_loss_level
            if not na(primary_support) and primary_support > sl_price
                sl_price := primary_support - atr_value * 0.2  // 支撐位下方一點
            if not na(lowest_in_vcp_lookback) and lowest_in_vcp_lookback < sl_price
                sl_price := lowest_in_vcp_lookback
            if not na(livermore_pivot_low_val) and livermore_pivot_low_val < sl_price
                sl_price := livermore_pivot_low_val

            resultText += "\n\n📍 **關鍵支撐位分析:**"
            if not na(primary_support)
                resultText += "\n   • 主要支撐: " + str.tostring(primary_support, "#.##") + " (距離當前價格 " + str.tostring((close - primary_support) / close * 100, "#.#") + "%)"
            if not na(secondary_support)
                resultText += "\n   • 次要支撐: " + str.tostring(secondary_support, "#.##") + " (距離當前價格 " + str.tostring((close - secondary_support) / close * 100, "#.#") + "%)"

            resultText += "\n🛡️ **建議止損價: " + str.tostring(sl_price, "#.##") + "**"
            resultText += "\n   基於價格行為分析，結合關鍵支撐位、VCP底部、Livermore關鍵點設置"
        else
            sl_price := entry_price - atr_value * stop_loss_atr_mult
            resultText += "\n🛡️ **建議止損價 (ATR): " + str.tostring(sl_price, "#.##") + "**"

        // 計算目標價位
        tp_price := entry_price + atr_value * profit_target_atr_mult
        if not na(primary_resistance)
            float resistance_target = primary_resistance * 0.98  // 阻力位前一點
            if resistance_target > tp_price
                tp_price := resistance_target

        risk_reward_ratio := (tp_price - entry_price) / (entry_price - sl_price)

        resultText += "\n🎯 **目標價位分析:**"
        if not na(primary_resistance)
            resultText += "\n   • 主要阻力: " + str.tostring(primary_resistance, "#.##") + " (距離當前價格 " + str.tostring((primary_resistance - close) / close * 100, "#.#") + "%)"
        if not na(secondary_resistance)
            resultText += "\n   • 次要阻力: " + str.tostring(secondary_resistance, "#.##") + " (距離當前價格 " + str.tostring((secondary_resistance - close) / close * 100, "#.#") + "%)"

        resultText += "\n🎯 **建議止盈價: " + str.tostring(tp_price, "#.##") + "**"
        resultText += "\n📊 **風險回報比: 1:" + str.tostring(risk_reward_ratio, "#.##") + "**"

        if risk_reward_ratio < 2.0
            resultText += " ⚠️ (風險回報比偏低，建議謹慎)"
        else if risk_reward_ratio > 3.0
            resultText += " ✅ (風險回報比良好)"

        if enable_trailing_stop
            if trailing_stop_type == "ATR 倍數"
                resultText += "\n🔄 **追蹤止損**: 最高價 - " + str.tostring(trailing_stop_atr_mult, "#.##") + " × ATR"
            else if trailing_stop_type == "前一根K線低點"
                resultText += "\n🔄 **追蹤止損**: 前一根K線低點"
            else if trailing_stop_type == "短期均線"
                resultText += "\n🔄 **追蹤止損**: " + str.tostring(len_ma_short) + "日均線下方"
    else // Short trade
        // 做空邏輯類似，但方向相反
        if enable_price_action_stop_loss
            sl_price := price_action_stop_loss_level
            if not na(primary_resistance) and primary_resistance < sl_price
                sl_price := primary_resistance + atr_value * 0.2  // 阻力位上方一點
            if not na(livermore_pivot_high_val) and livermore_pivot_high_val > sl_price
                sl_price := livermore_pivot_high_val

            resultText += "\n\n📍 **關鍵阻力位分析:**"
            if not na(primary_resistance)
                resultText += "\n   • 主要阻力: " + str.tostring(primary_resistance, "#.##") + " (距離當前價格 " + str.tostring((primary_resistance - close) / close * 100, "#.#") + "%)"
            if not na(secondary_resistance)
                resultText += "\n   • 次要阻力: " + str.tostring(secondary_resistance, "#.##") + " (距離當前價格 " + str.tostring((secondary_resistance - close) / close * 100, "#.#") + "%)"

            resultText += "\n🛡️ **建議止損價: " + str.tostring(sl_price, "#.##") + "**"
        else
            sl_price := entry_price + atr_value * stop_loss_atr_mult
            resultText += "\n🛡️ **建議止損價 (ATR): " + str.tostring(sl_price, "#.##") + "**"

        tp_price := entry_price - atr_value * profit_target_atr_mult
        if not na(primary_support)
            float support_target = primary_support * 1.02  // 支撐位上方一點
            if support_target < tp_price
                tp_price := support_target

        risk_reward_ratio := (entry_price - tp_price) / (sl_price - entry_price)

        resultText += "\n🎯 **目標價位分析:**"
        if not na(primary_support)
            resultText += "\n   • 主要支撐: " + str.tostring(primary_support, "#.##") + " (距離當前價格 " + str.tostring((close - primary_support) / close * 100, "#.#") + "%)"

        resultText += "\n🎯 **建議止盈價: " + str.tostring(tp_price, "#.##") + "**"
        resultText += "\n📊 **風險回報比: 1:" + str.tostring(risk_reward_ratio, "#.##") + "**"

    resultText

// --- 新增指標計算 ---
// 1. 趨勢判斷 (Minervini 趨勢樣板 & 短期均線排列)
// Minervini 趨勢樣板條件：股價高於50日、150日和200日均線；50日均線高於150日和200日均線；150日均線高於200日均線；200日均線至少上升1個月。
is_minervini_uptrend = close > ma_fast and ma_fast > ma_medium and ma_medium > ma_slow and ma_slow > ma_slow[20]
is_minervini_downtrend = close < ma_fast and ma_fast < ma_medium and ma_medium < ma_slow

// 短期趨勢判斷 (基於 10, 20, 50 日均線排列)
// 這是為了更靈敏地捕捉短期趨勢，符合短期交易的特性。
is_short_term_uptrend = close > ma_short and ma_short > ma_medium_short and ma_medium_short > ma_fast
is_short_term_downtrend = close < ma_short and ma_short < ma_medium_short and ma_medium_short < ma_fast

// 綜合趨勢判斷：Minervini 趨勢樣板優先，若不滿足則看短期趨勢
is_strong_uptrend = is_minervini_uptrend or (is_short_term_uptrend and not is_minervini_downtrend)
is_strong_downtrend = is_minervini_downtrend or (is_short_term_downtrend and not is_minervini_uptrend)

// Kristjan Kullamägi Episodic Pivot 增強檢測
is_gap_up = (open - close[1]) / close[1] * 100 > ep_gap_pct
is_ep_volume = volume > vol_ma * ep_vol_mult
is_ep_price_range = close >= ep_min_price and close <= ep_max_price
is_ep_relative_strength = relative_strength > ep_rs_threshold
is_ep_strong_close = close > (high + low) / 2 // 收盤價在當日中位數之上

// EP品質評分 (Kristjan Kullamägi 思路)
ep_quality_score = 0.0
if is_gap_up
    ep_quality_score += 1.0
if is_ep_volume
    ep_quality_score += 1.0
if is_ep_price_range
    ep_quality_score += 1.0
if is_ep_relative_strength
    ep_quality_score += 1.0
if is_ep_strong_close
    ep_quality_score += 1.0
if volume > ta.highest(volume, 50) * 0.8 // 近期高成交量
    ep_quality_score += 1.0

is_episodic_pivot = ep_enable and ep_quality_score >= 4.0 and is_strong_uptrend

// EP後盤整檢測
ep_consolidation_high = ta.highest(high, ep_consolidation_days)
ep_consolidation_low = ta.lowest(low, ep_consolidation_days)
ep_consolidation_range = (ep_consolidation_high - ep_consolidation_low) / ep_consolidation_low
is_ep_tight_consolidation = ep_consolidation_range < 0.15 // 盤整幅度小於15%

// EP突破確認
is_ep_breakout = close > ep_consolidation_high[1] and volume > vol_ma * ep_breakout_vol_mult

// --- 改進 2: Linda Raschke 的動量衰竭 (RSI 背離) ---
rsi = ta.rsi(close, rsi_length)
// 尋找價格高點和 RSI 高點的背離
price_pivot_high = ta.pivothigh(high, rsi_lookback, rsi_lookback)
rsi_pivot_high = ta.pivothigh(rsi, rsi_lookback, rsi_lookback)
// 判斷看跌背離：當價格創下新高，但RSI未能創下新高時，我們認為可能存在背離。
// 這裡使用一個簡化的邏輯來捕捉這個現象。
price_makes_higher_high = high > ta.highest(high, rsi_lookback)[1]
rsi_makes_lower_high = rsi < ta.highest(rsi, rsi_lookback)[1]
is_bearish_divergence = momentum_div_enable and price_makes_higher_high and rsi_makes_lower_high and is_strong_uptrend

// Linda Raschke 波動性分析增強
[bb_basis, bb_upper, bb_lower] = ta.bb(close, bb_length, bb_stddev)
bb_width = (bb_upper - bb_lower) / bb_basis
bb_width_sma = ta.sma(bb_width, 10)

// Keltner通道 (Linda Raschke 經典組合)
keltner_basis = ta.ema(close, keltner_length)
keltner_range = ta.atr(keltner_length) * keltner_mult
keltner_upper = keltner_basis + keltner_range
keltner_lower = keltner_basis - keltner_range

// TTM Squeeze (Linda Raschke 思路)
is_bb_squeeze = volatility_filter_enable and bb_upper < keltner_upper and bb_lower > keltner_lower
squeeze_momentum = ta.linreg(close - ta.sma(close, keltner_length), keltner_length, 0)

// 波動性突破檢測
bb_position = (close - bb_lower) / (bb_upper - bb_lower)
is_volatility_breakout = bb_width > bb_width_sma * volatility_breakout_mult

// Linda Raschke 2B模式 (假突破後反轉)
is_2b_top = high > bb_upper and close < bb_upper and close < close[1]
is_2b_bottom = low < bb_lower and close > bb_lower and close > close[1]

// --- 核心交易邏輯 ---

// 更高時間框架的趨勢判斷 (Minervini 思路)
[htf_close, htf_ma_fast, htf_ma_medium, htf_ma_slow] = request.security(syminfo.tickerid, higher_timeframe,
     [close, ma_fast, ma_medium, ma_slow])

is_htf_minervini_uptrend = htf_close > htf_ma_fast and htf_ma_fast > htf_ma_medium and htf_ma_medium > htf_ma_slow
is_htf_minervini_downtrend = htf_close < htf_ma_fast and htf_ma_fast < htf_ma_medium and htf_ma_medium < htf_ma_slow

// 更高時間框架的短期趨勢判斷
[htf_ma_short, htf_ma_medium_short] = request.security(syminfo.tickerid, higher_timeframe,
     [ma_short, ma_medium_short])

is_htf_short_term_uptrend = htf_close > htf_ma_short and htf_ma_short > htf_ma_medium_short and htf_ma_medium_short > htf_ma_fast
is_htf_short_term_downtrend = htf_close < htf_ma_short and htf_ma_short < htf_ma_medium_short and htf_ma_medium_short < htf_ma_fast

// 綜合更高時間框架趨勢判斷
is_htf_uptrend = is_htf_minervini_uptrend or (is_htf_short_term_uptrend and not is_htf_minervini_downtrend)
is_htf_downtrend = is_htf_minervini_downtrend or (is_htf_short_term_downtrend and not is_htf_minervini_uptrend)

// 結合多時間框架的趨勢判斷
is_overall_strong_uptrend = is_strong_uptrend and is_htf_uptrend
is_overall_strong_downtrend = is_strong_downtrend and is_htf_downtrend

// 2. VCP 波動性收縮分析 (Mark Minervini 思路)
// 條件：短期波動性顯著低於長期波動性
is_volatility_contracting = atr_short < atr_long * atr_contraction_ratio

// VCP 形態判斷 (Mark Minervini 思路)
// 條件：在指定回溯週期內，出現至少 min_vcp_contractions 次波動性收縮
is_vcp_pattern = false
vcp_contraction_count = 0
for i = 0 to vcp_lookback - 1
    if (atr_short[i] < atr_long[i] * atr_contraction_ratio)
        vcp_contraction_count += 1
if (vcp_contraction_count >= min_vcp_contractions)
    is_vcp_pattern := true

// 2.1 Minervini 杯柄形態識別 (Mark Minervini 思路)
// 杯部識別：尋找一個 U 形或 V 形的底部，通常伴隨左側高點和右側高點
// 這裡簡化為尋找一個相對較深的回調，然後價格回升
cup_high = ta.highest(high, cup_length)
cup_low = ta.lowest(low, cup_length)
cup_depth = cup_high - cup_low
is_cup_formed = cup_depth > atr_short * min_cup_depth_atr and close > cup_high * 0.8 // 價格從底部回升到杯部高點的80%以上

// 柄部識別：在杯部右側高點附近形成一個小幅回調，且波動性收縮
handle_high = ta.highest(high, handle_length)
handle_low = ta.lowest(low, handle_length)
handle_depth = handle_high - handle_low
is_handle_formed = is_cup_formed[handle_length] and handle_high < cup_high and handle_low > cup_high * (1 - handle_depth_ratio) and is_volatility_contracting

is_cup_with_handle = is_cup_formed and is_handle_formed

// 3. 成交量與價格行為分析
high_volume = volume > vol_ma * vol_mult
// 強化成交量枯竭判斷 (基於 Wyckoff 思路)
// 條件：連續 dry_up_bars 根 K 線的成交量都低於平均成交量，且價格波動收窄
volume_dry_up_condition = true
for i = 0 to dry_up_bars - 1
    if (volume[i] >= vol_ma[i] * vol_dryup_mult) // 成交量低於均量的一定倍數
        volume_dry_up_condition := false
        break
// 強化成交量枯竭判斷：連續 dry_up_bars 根 K 線的成交量都低於平均成交量，且價格波動收窄，同時當前成交量是過去 dry_up_bars * 2 根 K 線的最低量之一
volume_dry_up = volume_dry_up_condition and (high - low) < ta.atr(10) * 0.8 and volume <= ta.lowest(volume, dry_up_bars * 2) // 結合價格波動收窄和近期最低成交量 (Wyckoff 思路強化)
price_spread = math.max(high - low, 0.00001) // 避免除以零
close_position = (close - low) / price_spread

// 看漲信號 (口袋支點 - Pocket Pivot) (Mark Minervini 思路)
// 條件：陽線，成交量高於過去10天中任何一根陰線的成交量。
is_up_day = close > open
volume_check = volume > ta.highest(volume * (close < open ? 1 : 0), 10) // 陰線成交量
bullish_pocket_pivot = is_up_day and volume_check

// 看跌信號 (Upthrust) (Wyckoff 思路)
// 條件：高成交量 + 收盤在K線下半部 + 創下近期新高 (派發)
bearish_volume_thrust = high_volume and close_position < 0.4 and high > high[1]

// Wyckoff VSA 增強分析
spring_lookback = input.int(20, title="Spring回溯週期", group="Wyckoff VSA分析")
vsa_enable_professional_volume = input.bool(true, title="啟用專業成交量檢測", group="Wyckoff VSA分析")
vsa_ultra_high_volume_mult = input.float(3.0, title="超高成交量倍數", group="Wyckoff VSA分析")
vsa_low_volume_mult = input.float(0.5, title="低成交量倍數", group="Wyckoff VSA分析")

// VSA 成交量分類
is_ultra_high_volume = volume > vol_ma * vsa_ultra_high_volume_mult
is_high_volume_vsa = volume > vol_ma * 1.5 and volume <= vol_ma * vsa_ultra_high_volume_mult
is_average_volume = volume >= vol_ma * vsa_low_volume_mult and volume <= vol_ma * 1.5
is_low_volume_vsa = volume < vol_ma * vsa_low_volume_mult

// VSA 價差分析
price_spread_atr = (high - low) / ta.atr(14)
is_wide_spread = price_spread_atr > 1.5
is_narrow_spread_vsa = price_spread_atr < 0.7

// Wyckoff 細微信號
// 無供應測試 (No Supply): 下跌K線，價差小，成交量低於過去 N 根 K 線的平均成交量。表明賣壓枯竭。
is_down_bar = close < open
is_narrow_spread = (high - low) < (ta.atr(10) * 0.8)
is_low_volume_wyckoff = volume < ta.sma(volume, 5) * 0.7 // 成交量低於過去5天平均成交量的70%
no_supply_bar = is_down_bar and is_narrow_spread and is_low_volume_wyckoff

// VSA 專業成交量行為檢測
// 測試供應 (Test for Supply) - VSA增強
is_test_for_supply = is_down_bar and is_low_volume_vsa and volume < volume[1] * 0.8 and volume < volume[2] * 0.8 and close_position > 0.4 and is_narrow_spread_vsa

// 專業買入 (Professional Buying)
is_professional_buying = is_down_bar and is_ultra_high_volume and close_position > 0.6 and is_wide_spread

// 專業賣出 (Professional Selling)
is_professional_selling = is_up_day and is_ultra_high_volume and close_position < 0.4 and is_wide_spread

// 無需求上漲 (No Demand Up Move)
is_no_demand_up_move = is_up_day and is_low_volume_vsa and is_narrow_spread_vsa and close_position < 0.6

// 無需求測試 (No Demand): 上漲K線，價差小，成交量低於過去 N 根 K 線的平均成交量。表明需求不足。
no_demand_bar = is_up_day and is_narrow_spread and is_low_volume_wyckoff

// Wyckoff 需求進入 (Demand Coming In) - 強化版 (Wyckoff 思路)
// 條件：價格上漲，成交量顯著放大 (高於平均成交量的一定比例，且為近期高點)，且收盤價在 K 線中上部，表明買盤積極
// 強化：增加對成交量與前幾根K線的比較，確保是顯著放大的成交量
is_demand_coming_in = is_up_day and volume > vol_ma * 1.5 and volume > ta.highest(volume, 5) * 0.8 and volume > volume[1] * 1.2 and close_position > 0.6 and (high - low) > ta.atr(10) * 0.8 // 價差大，收盤好，成交量為近期高點

// Wyckoff Spring 增強
is_spring = low < ta.lowest(low[1], spring_lookback) and close > ta.lowest(low[1], spring_lookback) and is_high_volume_vsa

// Wyckoff UTAD (上衝回落) (Wyckoff 思路)
// 條件：價格突破近期高點後迅速跌回，且成交量放大。通常發生在派發階段。
utad_lookback = input.int(20, title="UTAD 回溯週期", group="Wyckoff 事件")
is_utad = high > ta.highest(high[1], utad_lookback) and close < ta.highest(high[1], utad_lookback) and volume > vol_ma * vol_mult * 1.0 // 成交量放大

// Wyckoff 震倉 (Shakeout) (Wyckoff 思路)
// 條件：價格快速跌破支撐位（通常是交易區間的低點），但隨後迅速收回，且伴隨巨量。
// 與 Spring 類似，但更強調對弱手的清洗。
shakeout_lookback = input.int(20, title="Shakeout 回溯週期", group="Wyckoff 事件")
is_shakeout = low < ta.lowest(low[1], shakeout_lookback) and close > ta.lowest(low[1], shakeout_lookback) and volume > vol_ma * vol_mult * 1.5 // 巨量確認

// Wyckoff 買入高潮 (Buying Climax, BC) (Wyckoff 思路)
// 條件：價格急劇上漲，成交量巨大，收盤不佳 (通常是長上影線或收盤在K線下半部)。
// 強化：價格漲幅大，成交量是近期最高，且收盤價在 K 線實體下半部。
is_price_surge_bc = (close - close[1]) > ta.atr(14) * 1.5 // 價格急劇上漲 (漲幅大於1.5倍ATR)
is_high_volume_climax_bc = volume > vol_ma * 2.5 and volume == ta.highest(volume, 10) // 成交量巨大且為近期最高
is_weak_close_bc = close_position < 0.4 // 收盤在K線下半部
is_buying_climax = is_price_surge_bc and is_high_volume_climax_bc and is_weak_close_bc

// Wyckoff 賣出高潮 (Selling Climax, SC) (Wyckoff 思路)
// 條件：價格急劇下跌，成交量巨大，收盤不佳 (通常是長下影線或收盤在K線上半部)。
// 強化：價格跌幅大，成交量是近期最高，且收盤價在 K 線實體上半部。
is_price_drop_surge_sc = (close[1] - close) > ta.atr(14) * 1.5 // 價格急劇下跌 (跌幅大於1.5倍ATR)
is_strong_volume_climax_sc = volume > vol_ma * 2.5 and volume == ta.highest(volume, 10) // 成交量巨大且為近期最高
is_strong_close_sc = close_position > 0.6 // 收盤在K線上半部
is_selling_climax = is_price_drop_surge_sc and is_strong_volume_climax_sc and is_strong_close_sc

// Wyckoff 自動反彈 (Automatic Rally, AR) (Wyckoff 思路)
// 條件：在賣出高潮 (SC) 之後，價格出現明顯反彈，且通常伴隨成交量放大。
// 強化：SC 後的陽線，且成交量放大，價格快速回升。
is_automatic_rally_sc = is_selling_climax[1] and close > open[1] and close > close[1] and volume > vol_ma * 1.5 // SC 後的陽線，且成交量放大

// Wyckoff 二次測試 (Secondary Test, ST) - 累積階段 (Wyckoff 思路)
// 條件：在自動反彈 (AR) 之後，價格回落再次測試賣出高潮 (SC) 的低點區域，但成交量顯著縮小。
// 這表明賣壓枯竭，是累積階段的重要信號。
is_secondary_test_sc = is_automatic_rally_sc[1] and close < close[1] and low <= ta.lowest(low, 5)[1] and volume < vol_ma * 0.8 // AR 後回落，測試低點，成交量縮小

// Wyckoff 二次測試 (Secondary Test, ST) - 派發階段 (Wyckoff 思路)
// 條件：在買入高潮 (BC) 或自動反彈 (AR - 派發) 之後，價格回落再次測試買入高潮 (BC) 的高點區域，但成交量顯著縮小。
// 這表明需求不足，是派發階段的重要信號。
is_secondary_test_bc = is_buying_climax[1] and close > close[1] and high >= ta.highest(high, 5)[1] and volume < vol_ma * 0.8 // BC 後反彈，測試高點，成交量縮小

// 假突破識別 (Jesse Livermore & Wyckoff 思路)
// 條件：價格突破關鍵高點後迅速跌回，且收盤價低於關鍵高點，同時成交量可能異常
// 強化假突破判斷：突破後迅速回落，且回落幅度超過一定比例，或收盤價跌回關鍵點下方
// K線形態組合
// 看漲吞噬 (Bullish Engulfing): 前一根是陰線，當前是陽線且實體完全吞噬前一根陰線實體
is_bullish_engulfing = close[1] > open[1] and close > open and close > open[1] and open < close[1]

// 看漲錘子線 (Hammer) - 潛在底部反轉信號 (價格行為思路)
// 條件：小實體，長下影線 (至少實體兩倍)，無或極短上影線，通常出現在下跌趨勢中。
is_hammer = (high - low) > (3 * math.abs(close - open)) and ((close - low) / (high - low) > 0.6 or (open - low) / (high - low) > 0.6) and (high - math.max(close, open)) < (0.1 * (high - low)) and close > open

// 看跌射擊之星 (Shooting Star) - 潛在頂部反轉信號 (價格行為思路)
// 條件：小實體，長上影線 (至少實體兩倍)，無或極短下影線，通常出現在上漲趨勢中。
is_shooting_star = (high - low) > (3 * math.abs(close - open)) and ((high - close) / (high - low) > 0.6 or (high - open) / (high - low) > 0.6) and (math.min(close, open) - low) < (0.1 * (high - low)) and close < open

// Nicolas Darvas 箱體突破
is_darvas_breakout_up = close > darvas_box_high[1] and volume > vol_ma * darvas_box_breakout_vol_mult
is_darvas_breakout_down = close < darvas_box_low[1] and volume > vol_ma * darvas_box_breakout_vol_mult

// 看跌吞噬 (Bearish Engulfing): 前一根是陽線，當前是陰線且實體完全吞噬前一根陽線實體
is_bearish_engulfing = close[1] < open[1] and close < open and close < open[1] and open > close[1]

// 內部 K 線 (Inside Bar) (價格行為思路)
// 條件：當前 K 線的最高價低於前一根 K 線的最高價，且最低價高於前一根 K 線的最低價。
// 表明市場波動性收縮，可能預示著盤整或潛在的突破/反轉。
is_inside_bar = high < high[1] and low > low[1]

// 外部 K 線 (Outside Bar) (價格行為思路)
// 條件：當前 K 線的最高價高於前一根 K 線的最高價，且最低價低於前一根 K 線的最低價。
// 表明市場波動性擴張，可能預示著趨勢的延續或反轉。
is_outside_bar = high > high[1] and low < low[1]

// 假突破識別 (Jesse Livermore & Wyckoff 思路)
// 條件：價格突破關鍵高點後迅速跌回，且收盤價低於關鍵高點，同時成交量可能異常
// 強化假突破判斷：突破後迅速回落，且回落幅度超過一定比例，或收盤價跌回關鍵點下方
false_breakout_retrace_ratio = input.float(0.5, title="假突破回落比例 (ATR)", group="假突破")

// 強化假突破判斷 (Jesse Livermore & Wyckoff 思路)
// 條件：價格突破關鍵高點/低點後迅速跌回/升回，且收盤價回到關鍵點內部，同時成交量可能異常
// 假突破後收盤價回到關鍵點內部，且回落/回升幅度超過一定比例，或出現 Wyckoff 細微信號
// 強化假突破判斷：結合成交量不足的條件
is_false_breakout_up = (high > livermore_pivot_high and close < livermore_pivot_high and close < high - atr_short * false_breakout_retrace_ratio and volume < vol_ma * 0.8) or is_utad or is_bearish_engulfing // 突破後收回，且收盤價回到關鍵點下方，回落幅度大，或出現 UTAD，或出現看跌吞噬
is_false_breakout_down = (low < livermore_pivot_low and close > livermore_pivot_low and close > low + atr_short * false_breakout_retrace_ratio and volume < vol_ma * 0.8) or is_spring or is_bullish_engulfing // 跌破後收回，且收盤價回到關鍵點上方，回升幅度大，或出現 Spring，或出現看漲吞噬

// 4. 買入/賣出設置 (Setup)
// 整合多策略買入設置
// 1. Minervini VCP設置 (波動性收縮 + 成交量枯竭 + 相對強度)
minervini_setup = is_vcp_pattern and (volume_dry_up or volume_dry_up[1]) and short_term_relative_strength > 0.7 and is_bb_squeeze

// 2. O'Neil CANSLIM設置 (相對強度 + 成交量激增 + 新高)
oneil_setup = is_rs_strong and is_volume_surge and is_near_new_high and price_change_12weeks > 20

// 3. Kullamägi EP設置 (高品質跳空缺口)
kullamagi_setup = is_episodic_pivot and ep_quality_score >= 5.0

// 4. Raschke 波動性設置 (Squeeze突破)
raschke_setup = is_bb_squeeze[1] and not is_bb_squeeze and squeeze_momentum > 0

// 5. Williams 短期反轉設置
williams_setup = williams_r_oversold_bounce and williams_r_bullish_divergence

// 綜合買入設置
long_setup = is_strong_uptrend and (minervini_setup or oneil_setup or kullamagi_setup or raschke_setup or williams_setup or is_cup_with_handle)

// 整合多策略回調買入設置
// Wyckoff VSA回調買入
wyckoff_retrace_setup = is_strong_uptrend and (is_test_for_supply or is_professional_buying or no_supply_bar)

// Minervini回調買入
minervini_retrace_setup = is_strong_uptrend and math.abs(close - ma_retrace) < atr_short * retrace_price_tolerance_atr_mult and volume < vol_ma * retrace_volume_mult

// Williams短期超賣回調
williams_retrace_setup = is_strong_uptrend and williams_r_oversold_bounce

// 綜合回調買入設置
is_retrace_buy_setup = wyckoff_retrace_setup or minervini_retrace_setup or williams_retrace_setup

// 新增：低成交量回調判斷 (Mark Minervini & Wyckoff 思路)
// 條件：處於上升趨勢，價格小幅回調 (例如，收盤價在開盤價下方或接近開盤價)，且成交量顯著低於平均水平，並出現 Wyckoff 累積信號。
// 這表明賣壓枯竭，是潛在的低風險買入機會。
// 改進二：結合 Wyckoff 的「測試供應」和「需求進入」信號優化回調買入
// 策略來源: Wyckoff, Mark Minervini
is_low_volume_retrace = is_strong_uptrend and close <= open and volume < vol_ma * retrace_volume_mult and (is_test_for_supply or is_demand_coming_in or no_supply_bar) // 增加 Wyckoff 累積信號判斷

// Minervini 低風險買點：在 VCP 設置後，價格在快線附近小幅回調，且成交量枯竭。
// 這代表在突破前，供應被吸收，是潛在的低風險入場機會。
is_minervini_low_risk_entry = long_setup and math.abs(close - ma_fast) < ta.atr(10) * 0.5 and volume_dry_up

// 賣出設置：處於強力下降趨勢 + 出現看跌成交量推動
short_setup = is_strong_downtrend and bearish_volume_thrust

// --- 交易觸發 (結合 Livermore 確認原則) ---
// 買入條件：在滿足設置條件後，出現一個口袋支點信號作為突破確認，且突破當日成交量顯著放大，收盤價位於K線實體上部 (Dan Zanger 思路)
// 強化突破確認：加入突破回踩確認 (Jesse Livermore 思路)
is_breakout_retrace_confirmed = false
// 判斷前一根K線是否為突破K線
if (long_setup[2] and bullish_pocket_pivot[1] and volume[1] > vol_ma[1] * breakout_volume_mult and volume[1] > ta.highest(volume[2], breakout_volume_lookback) and close_position[1] > breakout_close_position)
    // 判斷當前K線是否回踩了突破位附近 (收盤價在突破K線低點上方，且回踩深度在 ATR 倍數內)
    // 這裡的突破位可以簡化為突破K線的低點或開盤價
    is_retrace = low > low[1] - atr_short * retrace_atr_mult and close > open // 回踩後再次上漲
    is_breakout_retrace_confirmed := is_retrace

// 強化突破確認 (Dan Zanger 思路)
// 條件：突破後連續 breakout_confirm_bars 根 K 線成交量保持活躍，且收盤價穩定在突破位上方
is_breakout_volume_sustained = true
is_breakout_price_sustained = true
for i = 1 to breakout_confirm_bars
    if (volume[i] < vol_ma[i] * 1.2) // 突破後成交量仍需高於均量
        is_breakout_volume_sustained := false
    if (close[i] < close[i+1] * 0.98) // 突破後價格不應大幅回落
        is_breakout_price_sustained := false

// 強化突破 K 線的形態判斷 (Dan Zanger 思路)
// 條件：突破 K 線實體佔 K 線總長度的比例較大，且收盤價接近最高價
breakout_candle_body_ratio = input.float(0.6, title="突破 K 線實體比例", group="突破", tooltip="突破 K 線實體佔 K 線總長度的最小比例 (0-1)")
breakout_close_near_high_ratio = input.float(0.9, title="突破 K 線收盤價接近高點比例", group="突破", tooltip="突破 K 線收盤價距離高點的最小比例 (0-1)")

is_strong_breakout_candle = (close - open) / (high - low) > breakout_candle_body_ratio and (high - close) / (high - low) < (1 - breakout_close_near_high_ratio)

// 整合多策略買入觸發條件
is_zanger_volume_surge = volume > volume[1] * 2.0

// 1. 結構性突破 (多策略整合)
breakout_trigger = bullish_pocket_pivot and volume > vol_ma * breakout_volume_mult and volume > ta.highest(volume[1], breakout_volume_lookback) and close_position > breakout_close_position and is_strong_breakout_candle and is_zanger_volume_surge

// 各策略突破確認
minervini_breakout = minervini_setup[1] and breakout_trigger
oneil_breakout = oneil_setup[1] and breakout_trigger
kullamagi_breakout = kullamagi_setup[1] and breakout_trigger
raschke_breakout = raschke_setup and close > open
ep_breakout = is_ep_breakout and kullamagi_setup[ep_consolidation_days]

is_standard_breakout = minervini_breakout or oneil_breakout or kullamagi_breakout or raschke_breakout or ep_breakout

// 2. 回調買入確認
is_retrace_buy = is_retrace_buy_setup and close > open and volume > volume[1] * 1.1

// 3. VSA需求進入
is_demand_entry = is_demand_coming_in or is_professional_buying

// 4. Williams反轉確認
is_williams_entry = williams_setup and close > open

// 綜合買入條件
long_condition = is_overall_strong_uptrend and (is_standard_breakout or is_retrace_buy or is_demand_entry or is_williams_entry)

// 賣出條件：在設置K線出現後，下一根K線收盤價低於設置K線的低點
short_condition = is_overall_strong_downtrend and (short_setup[1] and close < low[1] or (is_bb_squeeze[1] and is_inside_bar and close < open)) // 增加內部 K 線在波動性收縮後的賣出機會 (價格行為思路)

// --- Wyckoff 價量分析 ---
// 努力與結果 (Effort vs. Result)
price_change = close - close[1]
volume_change = volume - volume[1]
is_price_up_vol_up = price_change > 0 and volume_change > 0 // 價漲量增 (和諧)
is_price_up_vol_down = price_change > 0 and volume_change < 0 // 價漲量縮 (分歧 - 需求減弱)
is_price_down_vol_up = price_change < 0 and volume_change > 0 // 價跌量增 (分歧 - 供應增加)
is_price_down_vol_down = price_change < 0 and volume_change < 0 // 價跌量縮 (和諧 - 拋壓減輕)

// --- 策略解說文字 ---
var string market_stage_text = "N/A"
var string signal_text = "N/A"
var string insight_text = "N/A"
var string action_text = "N/A"
var string market_type_text = "N/A"
var string strategy_core_text = "N/A"

// === 調用核心分析函數 ===
[primary_support, secondary_support, primary_resistance, secondary_resistance] = get_key_levels()
[total_market_score, trend_score, volume_score, momentum_score, structure_score] = get_market_stage_score(is_minervini_uptrend, is_short_term_uptrend, is_minervini_downtrend, is_short_term_downtrend, is_htf_minervini_uptrend, is_htf_minervini_downtrend, is_volume_surge, volume_dry_up, high_volume, is_demand_coming_in, relative_strength, williams_r, is_bearish_divergence, is_vcp_pattern, is_cup_with_handle, is_episodic_pivot, is_bb_squeeze)

// === 指標聯動邏輯增強 ===
// 計算指標一致性評分
float indicator_consensus_score = 0.0
int consensus_count = 0

// 1. 趨勢指標一致性檢查
bool trend_bullish = is_overall_strong_uptrend
bool trend_bearish = is_overall_strong_downtrend
if trend_bullish
    indicator_consensus_score += 25
    consensus_count += 1
else if trend_bearish
    indicator_consensus_score -= 25
    consensus_count += 1

// 2. 成交量指標一致性檢查
bool volume_confirms_trend = false
if trend_bullish and (is_price_up_vol_up or bullish_pocket_pivot)
    volume_confirms_trend := true
    indicator_consensus_score += 20
    consensus_count += 1
else if trend_bearish and (is_price_down_vol_up or bearish_volume_thrust)
    volume_confirms_trend := true
    indicator_consensus_score -= 20
    consensus_count += 1

// 3. 動量指標一致性檢查
bool momentum_aligned = false
if trend_bullish and williams_r > -50 and not is_bearish_divergence
    momentum_aligned := true
    indicator_consensus_score += 15
    consensus_count += 1
else if trend_bearish and williams_r < -50 and not is_bearish_divergence
    momentum_aligned := true
    indicator_consensus_score -= 15
    consensus_count += 1

// 4. 結構指標一致性檢查
bool structure_supports = false
if trend_bullish and (is_vcp_pattern or is_cup_with_handle or is_minervini_low_risk_entry)
    structure_supports := true
    indicator_consensus_score += 20
    consensus_count += 1
else if trend_bearish and (is_false_breakout_up or is_buying_climax)
    structure_supports := true
    indicator_consensus_score -= 20
    consensus_count += 1

// 5. 價位指標一致性檢查
bool price_level_aligned = false
if trend_bullish and not na(primary_support) and close > primary_support * 1.02
    price_level_aligned := true
    indicator_consensus_score += 10
    consensus_count += 1
else if trend_bearish and not na(primary_resistance) and close < primary_resistance * 0.98
    price_level_aligned := true
    indicator_consensus_score -= 10
    consensus_count += 1

// 計算最終一致性評分 (0-100)
float final_consensus_score = consensus_count > 0 ? math.abs(indicator_consensus_score) / consensus_count * 20 : 0

// 生成聯動分析文字
string indicator_synergy_text = ""
if final_consensus_score > 80
    indicator_synergy_text := "🟢 指標高度一致 (" + str.tostring(final_consensus_score, "#") + "/100)"
    indicator_synergy_text += "\n多個指標強烈確認當前趨勢，信號可靠性極高"
else if final_consensus_score > 60
    indicator_synergy_text := "🟡 指標基本一致 (" + str.tostring(final_consensus_score, "#") + "/100)"
    indicator_synergy_text += "\n大部分指標支持當前判斷，信號較為可靠"
else if final_consensus_score > 40
    indicator_synergy_text := "🟠 指標部分分歧 (" + str.tostring(final_consensus_score, "#") + "/100)"
    indicator_synergy_text += "\n指標間存在分歧，建議謹慎操作"
else
    indicator_synergy_text := "🔴 指標嚴重分歧 (" + str.tostring(final_consensus_score, "#") + "/100)"
    indicator_synergy_text += "\n指標間衝突明顯，建議觀望等待"

// === 市場階段量化判斷 ===
string market_stage_detail = ""
string market_confidence = ""

if total_market_score > 60
    market_stage_detail := "強勢上漲階段"
    market_confidence := "高信心度 (" + str.tostring(total_market_score, "#") + "/100)"
else if total_market_score > 30
    market_stage_detail := "溫和上漲階段"
    market_confidence := "中等信心度 (" + str.tostring(total_market_score, "#") + "/100)"
else if total_market_score > -30
    market_stage_detail := "盤整階段"
    market_confidence := "觀望 (" + str.tostring(total_market_score, "#") + "/100)"
else if total_market_score > -60
    market_stage_detail := "溫和下跌階段"
    market_confidence := "中等風險 (" + str.tostring(total_market_score, "#") + "/100)"
else
    market_stage_detail := "強勢下跌階段"
    market_confidence := "高風險 (" + str.tostring(total_market_score, "#") + "/100)"

// 重新設計解說邏輯 (結合 Wyckoff 階段判斷和量化評分)
// 1. 判斷主要市場階段
if (is_overall_strong_uptrend) // 使用結合多時間框架的趨勢判斷
    // 階段 2: 上升趨勢 (Wyckoff 標記為「標記上漲」)
    market_stage_text := "📈 " + market_stage_detail + " | " + market_confidence
    insight_text := "**市場分析評分**: 趨勢(" + str.tostring(trend_score, "#") + ") + 成交量(" + str.tostring(volume_score, "#") + ") + 動量(" + str.tostring(momentum_score, "#") + ") + 結構(" + str.tostring(structure_score, "#") + ") = " + str.tostring(total_market_score, "#") + "/100"
    insight_text += "\n\n" + indicator_synergy_text
    insight_text += "\n\n**Minervini 趨勢樣板成立**，機構資金主導市場，趨勢向上。"

    // 具體支撐阻力位分析
    if not na(primary_support)
        insight_text += "\n\n📍 **關鍵價位分析**:"
        insight_text += "\n• **主要支撐**: " + str.tostring(primary_support, "#.##") + " (下跌空間 " + str.tostring((close - primary_support) / close * 100, "#.#") + "%)"
        if not na(secondary_support)
            insight_text += "\n• **次要支撐**: " + str.tostring(secondary_support, "#.##") + " (下跌空間 " + str.tostring((close - secondary_support) / close * 100, "#.#") + "%)"

    if not na(primary_resistance)
        insight_text += "\n• **主要阻力**: " + str.tostring(primary_resistance, "#.##") + " (上漲空間 " + str.tostring((primary_resistance - close) / close * 100, "#.#") + "%)"
        if not na(secondary_resistance)
            insight_text += "\n• **次要阻力**: " + str.tostring(secondary_resistance, "#.##") + " (上漲空間 " + str.tostring((secondary_resistance - close) / close * 100, "#.#") + "%)"

    action_text := "🎯 **操作建議**: 順勢而為，尋找低風險買點"

    // 具體入場時機建議
    if not na(primary_support) and close > primary_support * 1.02
        action_text += "\n• **回調買入機會**: 當價格回調至 " + str.tostring(primary_support * 1.01, "#.##") + " - " + str.tostring(primary_support * 1.03, "#.##") + " 區間時考慮買入"

    if not na(primary_resistance)
        action_text += "\n• **突破買入機會**: 當價格突破 " + str.tostring(primary_resistance, "#.##") + " 且伴隨放量時考慮追漲"

    if (relative_strength > 0.8) // William J. O'Neil 相對強度判斷
        insight_text += "\n\n💪 **William J. O'Neil 相對強度分析**: RS " + str.tostring(relative_strength * 100, "#.##") + "% (強勁)"
        insight_text += "\n股價表現優於市場，具備潛力股特徵，建議優先關注。"
        action_text += "\n• **優先級**: ⭐⭐⭐ 高優先級標的"
    else if (relative_strength > 0.6)
        insight_text += "\n\n💪 **William J. O'Neil 相對強度分析**: RS " + str.tostring(relative_strength * 100, "#.##") + "% (中等)"
        insight_text += "\n股價表現中等，可適度關注。"
        action_text += "\n• **優先級**: ⭐⭐ 中等優先級標的"
    else if (relative_strength < 0.5)
        insight_text += "\n\n⚠️ **William J. O'Neil 相對強度分析**: RS " + str.tostring(relative_strength * 100, "#.##") + "% (較弱)"
        insight_text += "\n股價表現不如市場，即使處於上升趨勢也需謹慎。"
        action_text += "\n• **優先級**: ⭐ 低優先級，建議等待相對強度改善"
   
    // 短期相對強度判斷 (William J. O'Neil 思路)
    if (short_term_relative_strength > 0.7)
        insight_text += "\n> William J. O'Neil 思路：短期相對強度強勁 (短期 RS " + str.tostring(short_term_relative_strength * 100, "#.##") + "%)，表明股價在短期內表現突出，是短期交易的有利信號。"
        action_text := "在趨勢中尋找短期買入機會。"
    else if (short_term_relative_strength < 0.3)
        insight_text += "\n> William J. O'Neil 思路：短期相對強度較弱 (短期 RS " + str.tostring(short_term_relative_strength * 100, "#.##") + "%)，表明股價在短期內表現不佳，即使處於上升趨勢也需謹慎。"
        action_text := "避免短期介入，或等待短期相對強度改善。"

    if (williams_setup)
        signal_text := "Williams短期反轉設置"
        insight_text += "\n> Larry Williams思路：Williams %R超賣反彈(" + str.tostring(williams_r, "#.##") + ")，結合背離信號，短期反轉機會。"
        action_text := "關注短期反彈，配合其他信號確認。"

    if (oneil_setup)
        signal_text := "O'Neil CANSLIM設置 (RS:" + str.tostring(rs_rating, "#") + ")"
        insight_text += "\n> William O'Neil思路：相對強度排名" + str.tostring(rs_rating, "#") + "，成交量激增" + str.tostring(volume_surge, "#") + "%，接近新高，符合CANSLIM標準。"
        action_text := "優質成長股，關注突破買點。"

    if (kullamagi_setup)
        signal_text := "Kullamägi EP設置 (品質評分: " + str.tostring(ep_quality_score, "#.#") + ")"
        insight_text := "Kristjan Kullamägi思路：高品質向上跳空缺口，具備強勢特徵：跳空幅度大、成交量激增、相對強度強、收盤強勢。"
        action_text := "關注EP後" + str.tostring(ep_consolidation_days) + "天內的盤整突破機會。"
    else if (is_cup_with_handle) // Mark Minervini 思路
        signal_text := "Minervini 杯柄形態形成"
        insight_text := "Mark Minervini 思路：已識別出杯柄形態，這是一種強烈的看漲模式，表明在經歷回調後，供應已被吸收，為潛在的突破做準備。"
        action_text := "密切關注，等待突破買點出現。"
    else if (is_vcp_pattern and (volume_dry_up or volume_dry_up[1])) // Mark Minervini 思路
        signal_text := "VCP 買入設置 (吸籌後期)"
        insight_text := "Mark Minervini 思路：波動性收縮 (VCP) 且成交量枯竭。供應被吸收，為突破做準備 (Wyckoff 吸籌後期)。"
        if (is_bb_squeeze)
            insight_text += "\n> Linda Raschke 思路：同時偵測到布林帶寬度極度收縮 (Squeeze)，表明市場能量正在積蓄，突破的可能性和力度增加。"
        action_text := "高度關注，等待關鍵點放量突破。"
    else if (is_retrace_buy_setup) // Mark Minervini & Jesse Livermore 思路：回調買入
        signal_text := "回調買入設置 (低風險)"
        insight_text := "在強勢上升趨勢中，價格回調至關鍵均線 (如 " + str.tostring(retrace_ma_length) + " 日均線) 附近，且成交量顯著萎縮。這表明回調是健康的，賣壓減輕，提供了低風險的入場機會。Mark Minervini 提倡在趨勢中尋找這種回調買點，Jesse Livermore 也強調在關鍵支撐位附近尋找買入機會。"
        action_text := "可考慮在回調均線附近尋找買入機會，並結合其他信號確認。"
    else if (is_low_volume_retrace) // 新增：低成交量回調買入策略解說 (Mark Minervini & Wyckoff 思路)
        signal_text := "低成交量回調 (潛在買點)"
        insight_text := "在強勢上升趨勢中，價格小幅回調，且成交量顯著低於平均水平。這表明賣壓枯竭，是潛在的低風險買入機會。Mark Minervini 強調在趨勢中尋找這種回調買點，而 Wyckoff 的價量分析也認為低成交量回調是供應枯竭的信號，預示著價格可能再次上漲。"
        action_text := "可考慮在回調結束、價格恢復上漲動能時尋找買入機會，並結合其他信號確認。"
    else if (is_minervini_low_risk_entry) // Mark Minervini 思路：低風險買點
        signal_text := "Minervini 低風險買點"
        insight_text := "在 VCP 設置後，價格在快線附近小幅回調，且成交量枯竭。這是 Mark Minervini 強調的低風險入場機會，通常在突破前出現，表明在突破前供應已被充分吸收，是主力吸籌的最後階段。此時入場，潛在虧損較小，潛在收益較大。"
        action_text := "可考慮小倉位試單，或等待關鍵點放量突破確認。"
    else if (bullish_pocket_pivot)
        signal_text := "口袋支點 (需求強勁)"
        insight_text := "機構在趨勢中積極建倉的信號，需求強勁。"
        action_text := "可作為早期介入或加倉的依據。"
    else if (is_price_up_vol_down)
        signal_text := "價漲量縮 (需求減弱警示)"
        insight_text := "Wyckoff 價量分歧：上漲動能可能減弱，需求不足。可能進入派發前期。"
        action_text := "謹慎追高，觀察後續能否補量或轉入盤整。"
    else if (is_price_down_vol_up) // Wyckoff 努力與結果：價跌量增
        signal_text := "價跌量增 (供應增加警示)"
        insight_text := "Wyckoff 價量分歧：下跌動能增強，供應增加。在上升趨勢中可能是回調或趨勢反轉的早期信號。"
        action_text := "注意風險，考慮減倉或觀望。"
    else if (is_price_down_vol_down) // Wyckoff 努力與結果：價跌量縮
        signal_text := "價跌量縮 (拋壓減輕)"
        insight_text := "Wyckoff 價量和諧：下跌動能減弱，拋售壓力減輕。在上升趨勢中可能是健康回調的跡象。"
        action_text := "觀察是否在支撐位獲得支撐，等待買入機會。"
    else if (is_price_up_vol_up) // Wyckoff 努力與結果：價漲量增
        signal_text := "價漲量增 (需求強勁)"
        insight_text := "Wyckoff 價量和諧：上漲動能強勁，需求主導市場。趨勢健康。"
        action_text := "順勢而為，持有或尋找加倉機會。"
    else
        signal_text := "趨勢持續 (需求主導)"

    if (is_false_breakout_up) // Jesse Livermore & Wyckoff 思路：假突破
        signal_text := "假突破警示 (上漲)"
        insight_text := "價格短暫突破關鍵高點後迅速回落，且成交量不足，回落幅度大。這可能是主力誘多行為，或市場缺乏持續上漲的動力。結合 Wyckoff 的 UTAD (上衝回落) 信號，進一步確認了假突破的可能性。Jesse Livermore 曾強調，假突破是市場陷阱，應避免追高。"
        action_text := "謹慎追高，考慮減倉或觀望。"

    if (is_bearish_divergence) // Linda Raschke 思路
        signal_text := "動量衰竭警示 (RSI 看跌背離)"
        insight_text += "\n> Linda Raschke 思路：價格創出新高，但 RSI 指標未能同步創出新高，形成看跌背離。這表明上漲動能可能正在衰竭，是趨勢反轉的潛在預警信號。"
        action_text := "注意風險，考慮部分止盈，或收緊追蹤止損。"

else if (is_overall_strong_downtrend) // 使用結合多時間框架的趨勢判斷
    // 階段 4: 下降趨勢 (Wyckoff 標記為「標記下跌」)
    market_stage_text := "📉 " + market_stage_detail + " | " + market_confidence
    insight_text := "**市場分析評分**: 趨勢(" + str.tostring(trend_score, "#") + ") + 成交量(" + str.tostring(volume_score, "#") + ") + 動量(" + str.tostring(momentum_score, "#") + ") + 結構(" + str.tostring(structure_score, "#") + ") = " + str.tostring(total_market_score, "#") + "/100"
    insight_text += "\n\n" + indicator_synergy_text
    insight_text += "\n\n**空方力量強勁**，趨勢向下，主力正在派發或已完成派發。"

    // 具體支撐阻力位分析
    if not na(primary_resistance)
        insight_text += "\n\n📍 **關鍵價位分析**:"
        insight_text += "\n• **主要阻力**: " + str.tostring(primary_resistance, "#.##") + " (反彈空間 " + str.tostring((primary_resistance - close) / close * 100, "#.#") + "%)"
        if not na(secondary_resistance)
            insight_text += "\n• **次要阻力**: " + str.tostring(secondary_resistance, "#.##") + " (反彈空間 " + str.tostring((secondary_resistance - close) / close * 100, "#.#") + "%)"

    if not na(primary_support)
        insight_text += "\n• **主要支撐**: " + str.tostring(primary_support, "#.##") + " (下跌空間 " + str.tostring((close - primary_support) / close * 100, "#.#") + "%)"
        if not na(secondary_support)
            insight_text += "\n• **次要支撐**: " + str.tostring(secondary_support, "#.##") + " (下跌空間 " + str.tostring((close - secondary_support) / close * 100, "#.#") + "%)"

    action_text := "🛡️ **操作建議**: 規避風險，保護資本為主"

    // 具體操作建議
    if not na(primary_resistance) and close < primary_resistance * 0.98
        action_text += "\n• **反彈賣出機會**: 當價格反彈至 " + str.tostring(primary_resistance * 0.97, "#.##") + " - " + str.tostring(primary_resistance * 0.99, "#.##") + " 區間時考慮減倉"

    if not na(primary_support)
        action_text += "\n• **支撐破位警示**: 若價格跌破 " + str.tostring(primary_support, "#.##") + " 且伴隨放量，趨勢可能加速下跌"

    // Larry Williams %R 超買判斷 (結合短期交易)
    is_williams_r_overbought = williams_r > -20
    if (is_williams_r_overbought)
        insight_text += "\n\n⚠️ **Larry Williams %R 分析**: " + str.tostring(williams_r, "#.##") + " (超買區)"
        insight_text += "\n短期內價格可能被高估，存在進一步回調風險。"
        action_text += "\n• **短期策略**: 避免抄底，等待超賣反彈機會"
    else if (williams_r < -80)
        insight_text += "\n\n💡 **Larry Williams %R 分析**: " + str.tostring(williams_r, "#.##") + " (超賣區)"
        insight_text += "\n短期內可能出現技術性反彈，但需注意趨勢仍然向下。"
        action_text += "\n• **短期策略**: 可關注短期反彈機會，但嚴格控制風險"

    if (short_setup) // 看跌派發設置，通常是階段 4 啟動前的最後派發或回調
        signal_text := "看跌派發設置 (派發後期)"
        insight_text += "\n> 高成交量長上影線，疑似主力派發 (Wyckoff 派發)。"
        action_text := "風險極高，考慮減倉或規避。"
    else if (is_price_down_vol_down)
        signal_text := "價跌量縮 (供應減弱警示)"
        insight_text := "拋售壓力減輕，可能接近短期底部，或進入累積前期。"
        action_text := "空頭可考慮部分止盈，多頭需等待趨勢反轉信號。"
    else if (is_price_up_vol_up) // Wyckoff 努力與結果：價漲量增
        signal_text := "價漲量增 (需求強勁警示)"
        insight_text := "Wyckoff 價量分歧：在下降趨勢中出現價漲量增，可能是空頭回補或短期反彈，但需警惕是否為派發後的最後拉升。"
        action_text := "謹慎追高，觀察是否能突破下降趨勢。"
    else if (is_price_up_vol_down) // Wyckoff 努力與結果：價漲量縮
        signal_text := "價漲量縮 (需求不足)"
        insight_text := "Wyckoff 價量和諧：在下降趨勢中，反彈無力，需求不足。趨勢持續。"
        action_text := "規避風險，或尋找做空機會。"
    else if (is_price_down_vol_up) // Wyckoff 努力與結果：價跌量增
        signal_text := "價跌量增 (供應強勁)"
        insight_text := "Wyckoff 價量和諧：下跌動能強勁，供應主導市場。趨勢健康。"
        action_text := "規避風險，或尋找做空機會。"
    else
        signal_text := "趨勢持續 (供應主導)"

    if (is_false_breakout_down) // Jesse Livermore & Wyckoff 思路：假跌破
        signal_text := "假跌破警示 (下跌)"
        insight_text := "價格短暫跌破關鍵低點後迅速回升，且成交量不足，回升幅度大。這可能是主力誘空行為，或市場缺乏持續下跌的動力。結合 Wyckoff 的 Spring (彈簧) 信號，進一步確認了假跌破的可能性。應避免盲目殺跌。"
        action_text := "謹慎殺跌，考慮觀望或尋找反彈機會。"

else // 盤整階段 (Wyckoff 階段 1: 累積 或 階段 3: 派發)
    // 根據 Wyckoff 細微信號判斷是累積還是派發 (Wyckoff 思路強化)
    bool is_accumulation_signs = is_test_for_supply or is_demand_coming_in or no_supply_bar or is_spring or is_shakeout or is_selling_climax or is_automatic_rally_sc or is_secondary_test_sc or is_bullish_engulfing or is_hammer // 新增錘子線和 Shakeout
    bool is_distribution_signs = is_buying_climax or is_utad or no_demand_bar or bearish_volume_thrust or is_automatic_rally_sc or is_secondary_test_bc or is_bearish_engulfing or is_shooting_star // 新增射擊之星

    if (is_accumulation_signs and not is_distribution_signs)
        market_stage_text := "🔄 Wyckoff 階段 1: 累積 (吸籌) | " + market_confidence
        insight_text := "**市場分析評分**: " + str.tostring(total_market_score, "#") + "/100 (盤整偏多)"
        insight_text += "\n\n" + indicator_synergy_text
        insight_text += "\n\n**市場處於累積階段**，主力正在吸籌。特徵：價格波動收窄，成交量萎縮，賣壓枯竭。"

        // 具體價位分析
        if not na(primary_support) and not na(primary_resistance)
            float consolidation_range = (primary_resistance - primary_support) / primary_support * 100
            insight_text += "\n\n📍 **盤整區間分析**:"
            insight_text += "\n• **盤整區間**: " + str.tostring(primary_support, "#.##") + " - " + str.tostring(primary_resistance, "#.##")
            insight_text += "\n• **區間幅度**: " + str.tostring(consolidation_range, "#.#") + "%"
            insight_text += "\n• **當前位置**: 距離支撐 " + str.tostring((close - primary_support) / primary_support * 100, "#.#") + "%, 距離阻力 " + str.tostring((primary_resistance - close) / close * 100, "#.#") + "%"

        action_text := "💡 **操作建議**: 耐心等待，尋找低風險買入機會"
        action_text += "\n• **左側交易**: 在 " + str.tostring(primary_support * 1.01, "#.##") + " 附近尋找買入機會"
        action_text += "\n• **右側交易**: 等待突破 " + str.tostring(primary_resistance, "#.##") + " 後追漲"
        action_text += "\n• **風險控制**: 跌破 " + str.tostring(primary_support * 0.98, "#.##") + " 立即止損"

    else if (is_distribution_signs and not is_accumulation_signs)
        market_stage_text := "🔄 Wyckoff 階段 3: 派發 (出貨) | " + market_confidence
        insight_text := "**市場分析評分**: " + str.tostring(total_market_score, "#") + "/100 (盤整偏空)"
        insight_text += "\n\n" + indicator_synergy_text
        insight_text += "\n\n**市場處於派發階段**，主力正在出貨。特徵：價格波動劇烈，成交量放大，需求不足。"

        // 具體價位分析
        if not na(primary_support) and not na(primary_resistance)
            float consolidation_range = (primary_resistance - primary_support) / primary_support * 100
            insight_text += "\n\n📍 **盤整區間分析**:"
            insight_text += "\n• **派發區間**: " + str.tostring(primary_support, "#.##") + " - " + str.tostring(primary_resistance, "#.##")
            insight_text += "\n• **區間幅度**: " + str.tostring(consolidation_range, "#.#") + "%"
            insight_text += "\n• **當前位置**: 距離支撐 " + str.tostring((close - primary_support) / primary_support * 100, "#.#") + "%, 距離阻力 " + str.tostring((primary_resistance - close) / close * 100, "#.#") + "%"

        action_text := "⚠️ **操作建議**: 規避風險，保護資本"
        action_text += "\n• **減倉時機**: 在 " + str.tostring(primary_resistance * 0.99, "#.##") + " 附近考慮減倉"
        action_text += "\n• **做空機會**: 跌破 " + str.tostring(primary_support, "#.##") + " 後可考慮做空"
        action_text += "\n• **風險控制**: 突破 " + str.tostring(primary_resistance * 1.02, "#.##") + " 立即止損"

    else
        market_stage_text := "🔄 " + market_stage_detail + " | " + market_confidence
        insight_text := "**市場分析評分**: " + str.tostring(total_market_score, "#") + "/100 (方向不明)"
        insight_text += "\n\n" + indicator_synergy_text
        insight_text += "\n\n**市場方向不明**，主力可能在吸籌或派發，需密切觀察價量行為。"

        if not na(primary_support) and not na(primary_resistance)
            insight_text += "\n\n📍 **關鍵觀察位**:"
            insight_text += "\n• **上方阻力**: " + str.tostring(primary_resistance, "#.##") + " (突破看多)"
            insight_text += "\n• **下方支撐**: " + str.tostring(primary_support, "#.##") + " (跌破看空)"

        action_text := "🔍 **操作建議**: 保持耐心，等待方向明確"
        action_text += "\n• **觀望為主**: 避免盲目交易，等待明確信號"
        action_text += "\n• **突破跟進**: 有效突破關鍵位後再行動"

    if (is_test_for_supply)
        signal_text := "Wyckoff: 測試供應 (累積跡象)"
        insight_text += "\n> 價格下跌，成交量極低 (遠低於均量且持續萎縮)，且收盤價在 K 線中上部。這是 Wyckoff 累積階段的重要信號，表明賣壓枯竭，主力正在測試市場上是否還有供應，為潛在的上漲做準備。這提供了低風險的左側交易機會。"
        action_text := "積極關注，可作為左側交易的入場依據。結合 Minervini 的低風險買點思路，此時入場潛在虧損較小。"
    else if (is_demand_coming_in)
        signal_text := "Wyckoff: 需求進入 (累積跡象)"
        insight_text += "\n> 價格上漲，成交量顯著放大 (高於均量且為近期高點)，且收盤價在 K 線中上部。這是 Wyckoff 累積階段或趨勢啟動的重要信號，表明買盤積極，需求正在進入市場，預示著潛在的上漲。這提供了右側交易的入場機會。"
        action_text := "積極關注，可作為右側交易的入場依據。符合 Dan Zanger 強調的放量上漲原則。"
    else if (no_supply_bar)
        signal_text := "Wyckoff: 無供應測試 (累積跡象)"
        insight_text += "\n> 價跌量縮，賣壓枯竭。這是非常看漲的信號，尤其在支撐位附近，暗示累積。Wyckoff 認為這是主力在測試市場上是否還有供應，如果沒有，則價格容易上漲。"
        action_text := "積極關注，可作為左側交易的入場依據。結合 Jesse Livermore 的關鍵點支撐，尋找買入機會。"
    else if (no_demand_bar)
        signal_text := "Wyckoff: 無需求測試 (派發跡象)"
        insight_text += "\n> 價漲量縮，需求不足。警惕上漲乏力，可能面臨回調，暗示派發。Wyckoff 認為這是主力在測試市場上是否還有需求，如果沒有，則價格容易下跌。"
        action_text := "避免追高，觀察是否跌破支撐。符合 Jesse Livermore 的趨勢反轉判斷。"
    else if (volume_dry_up and is_volatility_contracting)
        signal_text := "吸籌跡象 (累積階段)"
        insight_text += "\n> 成交量枯竭且波動收縮，表明賣壓減輕，可能是吸籌階段。這是 Mark Minervini VCP 形態的關鍵特徵，預示著潛在的突破。"
        action_text := "左側交易者可小倉位試單，右側交易者等待突破。結合 Minervini 的 VCP 策略，尋找低風險買點。"
    else if (bearish_volume_thrust)
        signal_text := "派發跡象 (派發階段)"
        insight_text := "出現放量上衝回落，警惕主力正在派發。Wyckoff 認為這是主力出貨的典型行為，價格可能面臨下跌。"
        action_text := "避免介入，等待趨勢明朗。符合 Jesse Livermore 的趨勢反轉判斷。"
    else if (is_price_up_vol_up) // Wyckoff 努力與結果：價漲量增
        signal_text := "價漲量增 (需求強勁)"
        insight_text := "Wyckoff 價量和諧：在盤整區間價漲量增，可能是累積階段的買入信號，或派發階段的最後拉升。需結合其他信號判斷。"
        action_text := "關注是否能突破區間，或警惕假突破。符合 Dan Zanger 對放量上漲的重視。"
    else if (is_price_up_vol_down) // Wyckoff 努力與結果：價漲量縮
        signal_text := "價漲量縮 (需求不足)"
        insight_text := "Wyckoff 價量分歧：在盤整區間價漲量縮，表明上漲動能不足，可能是派發階段的跡象。主力可能在悄悄出貨。"
        action_text := "避免追高，觀察是否跌破支撐。符合 Jesse Livermore 對趨勢反轉的警惕。"
    else if (is_price_down_vol_up) // Wyckoff 努力與結果：價跌量增
        signal_text := "價跌量增 (供應增加)"
        insight_text := "Wyckoff 價量分歧：在盤整區間價跌量增，表明下跌動能強勁，可能是派發階段的跡象。主力可能在積極出貨。"
        action_text := "規避風險，觀察是否跌破支撐。符合 Jesse Livermore 對趨勢反轉的警惕。"
    else if (is_price_down_vol_down) // Wyckoff 努力與結果：價跌量縮
        signal_text := "價跌量縮 (拋壓減輕)"
        insight_text := "Wyckoff 價量和諧：在盤整區間價跌量縮，表明下跌動能減弱，可能是累積階段的跡象。主力可能在測試底部。"
        action_text := "關注是否在支撐位獲得支撐，等待買入機會。符合 Wyckoff 對累積階段的判斷。"
    else if (is_hammer) // 新增錘子線解說
        signal_text := "看漲錘子線 (潛在底部反轉)"
        insight_text += "\n> 在下跌趨勢或盤整底部出現錘子線，表明市場在下跌後出現買盤支撐，是潛在的底部反轉信號。結合成交量判斷其有效性。"
        action_text := "密切關注，可作為左側交易的入場依據，但需等待後續確認。符合價格行為交易者的思路。"
    else if (is_shooting_star) // 新增射擊之星解說
        signal_text := "看跌射擊之星 (潛在頂部反轉)"
        insight_text += "\n> 在上漲趨勢或盤整頂部出現射擊之星，表明市場在上漲後出現賣壓，是潛在的頂部反轉信號。結合成交量判斷其有效性。"
        action_text := "謹慎操作，考慮減倉或觀望。符合價格行為交易者的思路。"
    else if (is_inside_bar) // 新增內部 K 線解說
        signal_text := "內部 K 線 (波動性收縮)"
        insight_text += "\n> 偵測到內部 K 線，表明市場波動性正在收縮，可能預示著盤整或即將到來的突破/反轉。短期交易者常利用此形態尋找突破機會。Linda Raschke 強調波動性收縮是能量積蓄的過程。"
        action_text := "密切關注後續 K 線的突破方向，可作為潛在入場或出場的預警信號。"
    else if (is_outside_bar) // 新增外部 K 線解說
        signal_text := "外部 K 線 (波動性擴張)"
        insight_text += "\n> 偵測到外部 K 線，表明市場波動性正在擴張，可能預示著趨勢的延續或反轉。短期交易者常利用此形態確認趨勢的強度或反轉的發生。Linda Raschke 認為波動性擴張是能量釋放的過程。"
        action_text := "關注其收盤價位置和成交量，判斷是趨勢延續還是反轉信號。"
    else
        signal_text := "無方向震盪 (階段不明)"

    if (is_buying_climax)
        signal_text := "Wyckoff: 買入高潮 (BC)"
        insight_text += "\n> 價格急劇上漲，成交量巨大，但收盤不佳 (通常是長上影線或收盤在 K 線下半部)。這是 Wyckoff 派發階段的早期信號，表明主力正在出貨，需求可能已達極限。Jesse Livermore 曾強調，當市場出現瘋狂的買盤時，往往是頂部信號。"
        action_text := "極度謹慎，考慮減倉或觀望。符合 Jesse Livermore 的趨勢反轉判斷。"
    else if (is_automatic_rally_sc) // 修正為 is_automatic_rally_sc
        signal_text := "Wyckoff: 自動反彈 (AR)"
        insight_text := "買入高潮後的快速反彈。這是 Wyckoff 派發階段的第二個信號，通常在 BC 後出現，標誌著交易區間的上限。在累積階段，自動反彈 (AR) 則發生在賣出高潮 (SC) 之後，是價格從 SC 低點快速反彈的過程，標誌著交易區間的上限。這兩種情況都定義了交易區間的邊界。"
        action_text := "觀察反彈力度，不宜追高。在派發階段，AR 提供了做空機會；在累積階段，AR 提供了做多機會。"
    else if (is_selling_climax)
        signal_text := "Wyckoff: 賣出高潮 (SC)"
        insight_text := "價格急劇下跌，成交量巨大，但收盤不佳 (通常是長下影線或收盤在 K 線上半部)。這是 Wyckoff 累積階段的早期信號，表明恐慌性拋售已達極限，主力可能開始吸籌。Jesse Livermore 曾強調，當市場出現恐慌性拋售時，往往是底部信號。"
        action_text := "密切關注，可能出現反彈機會。符合 Jesse Livermore 的趨勢反轉判斷。"
    else if (is_automatic_rally_sc)
        signal_text := "Wyckoff: 自動反彈 (AR - SC)"
        insight_text := "賣出高潮後的快速反彈。這是 Wyckoff 累積階段的第二個信號，通常在 SC 後出現，標誌著交易區間的上限。這定義了累積區間的上限。"
        action_text := "觀察反彈力度，不宜追高。在累積階段，AR 提供了做多機會。"
    else if (is_secondary_test_bc)
        signal_text := "Wyckoff: 二次測試 (ST - BC)"
        insight_text := "價格再次測試買入高潮後的反彈高點，成交量較小。這是 Wyckoff 派發階段的信號，表明需求不足，可能再次下跌。通常發生在自動反彈 (AR) 之後，價格回落再次測試買入高潮 (BC) 的高點區域，但成交量顯著縮小。這表明主力在測試市場上是否還有需求，如果沒有，則價格容易下跌。"
        action_text := "謹慎操作，考慮做空機會。符合 Jesse Livermore 對趨勢反轉的警惕。"
    else if (is_secondary_test_sc)
        signal_text := "Wyckoff: 二次測試 (ST - SC)"
        insight_text := "價格再次測試賣出高潮後的反彈低點，成交量較小。這是 Wyckoff 累積階段的信號，表明供應枯竭，可能再次上漲。通常發生在自動反彈 (AR) 之後，價格回落再次測試賣出高潮 (SC) 的低點區域，但成交量顯著縮小。這表明主力在測試市場上是否還有供應，如果沒有，則價格容易上漲。"
        action_text := "積極關注，尋找買入機會。符合 Jesse Livermore 對趨勢反轉的警惕。"
    else if (is_bullish_engulfing)
        signal_text := "看漲吞噬 (潛在反轉)"
        insight_text += "\n> 在盤整區間出現看漲吞噬，可能預示著累積階段的結束和上漲的開始。這是一種強烈的 K 線反轉信號，結合成交量判斷其有效性。"
        action_text := "密切關注，等待進一步確認。符合價格行為交易者的思路。"
    else if (is_bearish_engulfing)
        signal_text := "看跌吞噬 (潛在反轉)"
        insight_text += "\n> 在盤整區間出現看跌吞噬，可能預示著派發階段的結束和下跌的開始。這是一種強烈的 K 線反轉信號，結合成交量判斷其有效性。"
        action_text := "謹慎操作，考慮減倉。符合價格行為交易者的思路。"
    else if (is_shakeout) // 新增震倉解說
        signal_text := "Wyckoff: 震倉 (Shakeout)"
        insight_text += "\n> 價格快速跌破支撐位後迅速收回，且伴隨巨量。這是 Wyckoff 累積階段的最後一次清洗浮籌，表明主力已準備好拉升。與 Spring 類似，但更強調對弱手的清洗。這提供了低風險的買入機會。"
        action_text := "積極關注，可作為左側交易的入場依據。符合 Wyckoff 的累積階段判斷。"

// 2. 覆蓋觸發條件的解說 (最高優先級)
if (long_condition)
    market_stage_text := "買入信號觸發"
    // 判斷具體是哪種買入條件觸發的
    if (is_standard_breakout)
        signal_text := "結構性突破買入"
        insight_text := "偵測到一個結構性突破買入信號，基於以下一種或多種經典形態之後的放量突破：\n"
        if (is_vcp_pattern[1])
            insight_text += "> Mark Minervini 的 VCP 形態\n"
        if (is_cup_with_handle[1])
            insight_text += "> Mark Minervini 的杯柄形態\n"
        if (is_episodic_pivot[ep_consolidation_days])
            insight_text += "> Kristjan Kullamägi 的向上跳空缺口 (EP) 後盤整\n"
       
        insight_text += "突破行為同時滿足 Dan Zanger 的巨量原則和強勢收盤特徵。"
        if (is_bb_squeeze[1])
            insight_text += "\n> Linda Raschke 思路：此次突破是從極度波動性收縮 (Squeeze) 中爆發，增加了信號的可靠性。"

    else if (is_retrace_buy)
        signal_text := "趨勢回調買入"
        insight_text := "Mark Minervini & Wyckoff 思路：在確立的上升趨勢中，價格回調至關鍵均線，且成交量萎縮並出現需求信號（如測試供應），表明賣壓枯竭，是低風險的入場時機。"
    else if (is_demand_entry)
        signal_text := "Wyckoff 需求進入"
        insight_text := "Wyckoff 思路：偵測到明顯的需求進入信號，價格上漲伴隨成交量顯著放大，表明機構資金正在積極買入，趨勢有望延續。"

    action_text := "執行買入策略，建議入場價位: " + str.tostring(close, "#.##") + "。"
    if (is_breakout_retrace_confirmed)
        insight_text += "\n> Jesse Livermore 思路：突破後出現回踩確認，表明突破的有效性更高，降低了假突破的風險，提供了更穩健的入場時機。"
   
    float lowest_in_vcp_lookback_val = low[0]
    for i = 0 to vcp_lookback - 1
        lowest_in_vcp_lookback_val := math.min(lowest_in_vcp_lookback_val, low[i])

    action_text += get_enhanced_risk_management_text(true, close, atr_short, stop_loss_atr_mult, profit_target_atr_mult, enable_price_action_stop_loss, low[1], enable_trailing_stop, trailing_stop_type, len_ma_short, lowest_in_vcp_lookback_val, livermore_pivot_low[1], na, na, na, primary_support, secondary_support, primary_resistance, secondary_resistance)
else if (is_darvas_breakout_up) // Nicolas Darvas 思路：箱體突破
    market_stage_text := "Darvas 箱體突破"
    signal_text := "Darvas 箱體向上突破"
    insight_text := "價格突破 Darvas 箱體高點，且伴隨放量。\n這是 Nicolas Darvas 強調的買入信號，表明價格已脫離盤整區間，進入新的上升趨勢。"
    action_text := "考慮買入，建議入場價位: " + str.tostring(close, "#.##") + "。"
    action_text += get_enhanced_risk_management_text(true, close, atr_short, stop_loss_atr_mult, profit_target_atr_mult, enable_price_action_stop_loss, darvas_box_high[1] - atr_short * 0.5, enable_trailing_stop, trailing_stop_type, len_ma_short, na, na, na, darvas_box_high[1], na, primary_support, secondary_support, primary_resistance, secondary_resistance)
else if (is_darvas_breakout_down) // Nicolas Darvas 思路：箱體跌破
    market_stage_text := "Darvas 箱體跌破"
    signal_text := "Darvas 箱體向下突破"
    insight_text := "價格跌破 Darvas 箱體低點，且伴隨放量。\n這是 Nicolas Darvas 強調的賣出信號，表明價格已脫離盤整區間，進入新的下降趨勢。"
    action_text := "考慮賣出或避險，建議入場價位: " + str.tostring(close, "#.##") + "。"
    action_text += get_enhanced_risk_management_text(false, close, atr_short, stop_loss_atr_mult, profit_target_atr_mult, enable_price_action_stop_loss, darvas_box_low[1] + atr_short * 0.5, enable_trailing_stop, trailing_stop_type, len_ma_short, na, na, na, na, darvas_box_low[1], primary_support, secondary_support, primary_resistance, secondary_resistance)
else if (short_condition)
    market_stage_text := "關鍵點跌破"
    signal_text := "Livermore 賣出確認"
    insight_text := "派發設置後價格跌破前低，確認下降趨勢。\n這是賣出的關鍵信號，符合 Jesse Livermore 的趨勢確認原則。"
    action_text := "執行賣出或避險策略，建議入場價位: " + str.tostring(close, "#.##") + "。"
    action_text += get_enhanced_risk_management_text(false, close, atr_short, stop_loss_atr_mult, profit_target_atr_mult, enable_price_action_stop_loss, high[1], enable_trailing_stop, trailing_stop_type, len_ma_short, na, na, livermore_pivot_high[1], na, na, primary_support, secondary_support, primary_resistance, secondary_resistance)

// --- 繪圖 ---
// 繪製均線
plot(ma_short, color=color.new(color.yellow, 0), title="短期均線 (10)")
plot(ma_medium_short, color=color.new(color.purple, 0), title="中短期均線 (20)")
plot(ma_fast, color=color.new(color.orange, 0), title="快線 (50)")
plot(ma_medium, color=color.new(color.aqua, 50), title="中線 (150)")
plot(ma_slow, color=color.new(color.gray, 50), title="慢線 (200)")

// 繪製買賣信號
plotshape(long_condition, style=shape.labelup, location=location.belowbar, color=color.new(color.green, 0), size=size.small, text="買入", title="買入確認")
plotshape(short_condition, style=shape.labeldown, location=location.abovebar, color=color.new(color.red, 0), size=size.small, text="賣出", title="賣出確認")

// 繪製 Darvas 箱體突破信號
plotshape(is_darvas_breakout_up, style=shape.labelup, location=location.belowbar, color=color.new(color.blue, 60), size=size.small, text="箱體突破", title="Darvas 箱體向上突破")
plotshape(is_darvas_breakout_down, style=shape.labeldown, location=location.abovebar, color=color.new(color.orange, 60), size=size.small, text="箱體跌破", title="Darvas 箱體向下突破")

// 繪製 Wyckoff 細微信號
plotshape(no_supply_bar, style=shape.circle, location=location.belowbar, color=color.new(color.lime, 50), size=size.tiny, title="無供應測試")
plotshape(is_test_for_supply, style=shape.circle, location=location.belowbar, color=color.new(color.green, 0), size=size.small, text="測試供應", title="Wyckoff 測試供應")
plotshape(no_demand_bar, style=shape.circle, location=location.abovebar, color=color.new(color.fuchsia, 50), size=size.tiny, title="無需求測試")
plotshape(is_demand_coming_in, style=shape.circle, location=location.belowbar, color=color.new(color.blue, 0), size=size.small, text="需求進入", title="Wyckoff 需求進入")
plotshape(is_spring, style=shape.triangleup, location=location.belowbar, color=color.new(color.green, 0), size=size.small, text="Spring", title="Wyckoff Spring")
plotshape(is_utad, style=shape.triangledown, location=location.abovebar, color=color.new(color.red, 0), size=size.small, text="UTAD", title="Wyckoff UTAD")

// 繪製杯柄形態
plotshape(is_cup_with_handle, style=shape.triangleup, location=location.belowbar, color=color.new(color.fuchsia, 0), size=size.small, text="杯柄", title="杯柄形態")

// 繪製 Livermore 關鍵點
plot(livermore_pivot_high, color=color.new(color.purple, 0), style=plot.style_linebr, linewidth=1, title="Livermore 關鍵高點")
plot(livermore_pivot_low, color=color.new(color.purple, 0), style=plot.style_linebr, linewidth=1, title="Livermore 關鍵低點")

// 繪製 Darvas 箱體
plot(darvas_box_high, color=color.new(color.blue, 50), style=plot.style_stepline, linewidth=1, title="Darvas 箱體高點")
plot(darvas_box_low, color=color.new(color.blue, 50), style=plot.style_stepline, linewidth=1, title="Darvas 箱體低點")

// --- 改進繪圖 ---
// 繪製布林帶 (Linda Raschke 思路)
plot(bb_basis, "BB Basis", color=color.new(color.gray, 50))
p1 = plot(bb_upper, "BB Upper", color=color.new(color.gray, 50))
p2 = plot(bb_lower, "BB Lower", color=color.new(color.gray, 50))
fill(p1, p2, title = "BB Fill", color=color.new(color.gray, 95))

// 新增策略信號
plotshape(kullamagi_setup, style=shape.labelup, location=location.belowbar, color=color.new(color.orange, 0), size=size.normal, text="EP", title="Kullamägi EP")
plotshape(oneil_setup, style=shape.labelup, location=location.belowbar, color=color.new(color.blue, 0), size=size.small, text="CAN", title="O'Neil CANSLIM")
plotshape(williams_setup, style=shape.labelup, location=location.belowbar, color=color.new(color.yellow, 0), size=size.tiny, text="W", title="Williams反轉")
plotshape(raschke_setup, style=shape.labelup, location=location.belowbar, color=color.new(color.purple, 0), size=size.tiny, text="SQ", title="Raschke Squeeze")

plotshape(williams_r_bullish_divergence, style=shape.arrowup, location=location.belowbar, color=color.new(color.lime, 0), size=size.tiny, text="WD+", title="Williams看漲背離")
plotshape(williams_r_bearish_divergence, style=shape.arrowdown, location=location.abovebar, color=color.new(color.red, 0), size=size.tiny, text="WD-", title="Williams看跌背離")
plotshape(is_2b_top, style=shape.arrowdown, location=location.abovebar, color=color.new(color.red, 0), size=size.small, text="2B", title="Raschke 2B頂部")
plotshape(is_2b_bottom, style=shape.arrowup, location=location.belowbar, color=color.new(color.lime, 0), size=size.small, text="2B", title="Raschke 2B底部")

// --- 背景著色 ---
// 標示強力上升趨勢
bgcolor(is_strong_uptrend ? color.new(color.green, 90) : na, title="強力上升趨勢")
// 標示 VCP 階段 (波動性收縮 + 成交量枯竭)
bgcolor(is_volatility_contracting and volume_dry_up ? color.new(color.yellow, 85) : na, title="VCP 準備階段")
// 標示看漲/看跌的信號
bgcolor(bullish_pocket_pivot and not long_condition ? color.new(color.blue, 80) : na, title="口袋支點信號")
bgcolor(bearish_volume_thrust ? color.new(color.purple, 80) : na, title="看跌成交量推動")
// 標示高成交量
bgcolor(high_volume ? color.new(color.teal, 80) : na, title="高成交量")
// 標示成交量枯竭
bgcolor(volume_dry_up ? color.new(color.gray, 80) : na, title="成交量枯竭")

// VSA背景著色
bgcolor(is_professional_buying ? color.new(color.green, 80) : na, title="專業買入")
bgcolor(is_professional_selling ? color.new(color.red, 80) : na, title="專業賣出")
bgcolor(is_bb_squeeze ? color.new(color.purple, 85) : na, title="Squeeze")
bgcolor(is_ultra_high_volume ? color.new(color.yellow, 70) : na, title="超高成交量")

// 標示 Wyckoff 價量關係 (背景色)
bgcolor(is_price_up_vol_down ? color.new(color.red, 80) : na, title="價漲量縮 (需求減弱)") // 警示色
bgcolor(is_price_down_vol_up ? color.new(color.red, 80) : na, title="價跌量增 (供應增加)") // 警示色
bgcolor(is_price_up_vol_up ? color.new(color.green, 80) : na, title="價漲量增 (需求強勁)") // 健康色
bgcolor(is_price_down_vol_down ? color.new(color.lime, 80) : na, title="價跌量縮 (拋壓減輕)") // 健康色

// --- 增強策略解說表格 ---
var table explanation_table = table.new(position.bottom_right, 3, 7, border_width=1)

if (barstate.islast)
    // 清空舊表格，防止重繪
    table.clear(explanation_table, 0, 0, 2, 6)

    // 填充通用解說內容
    market_type_text := "適用於股票、期貨、加密貨幣等流動性較好的市場。"
    strategy_core_text := "本策略整合多位傳奇交易者精髓，透過量化評分系統和價位分析，提供具體的操作指導。"

    // 定義顏色
    color header_bg_color = color.new(#2E3440, 0)
    color content_bg_color = color.new(#3B4252, 10)
    color accent_bg_color = color.new(#5E81AC, 20)
    color text_color_header = color.new(color.white, 0)
    color text_color_content = color.new(color.white, 0)
    color text_color_accent = color.new(color.white, 0)

    // 填充表格標題
    table.cell(explanation_table, 0, 0, "📊 智能交易分析系統", bgcolor=header_bg_color, text_color=text_color_header, text_halign=text.align_center, text_size=size.normal)
    table.merge_cells(explanation_table, 0, 0, 2, 0)

    // 第 1 行: 市場階段與評分
    table.cell(explanation_table, 0, 1, "市場階段", bgcolor=header_bg_color, text_color=text_color_header, text_halign=text.align_center)
    table.cell(explanation_table, 1, 1, market_stage_text, bgcolor=content_bg_color, text_color=text_color_content, text_halign=text.align_left)
    table.cell(explanation_table, 2, 1, "評分: " + str.tostring(total_market_score, "#") + "/100", bgcolor=accent_bg_color, text_color=text_color_accent, text_halign=text.align_center)

    // 第 2 行: 關鍵價位
    string key_levels_text = ""
    if not na(primary_resistance)
        key_levels_text += "阻力: " + str.tostring(primary_resistance, "#.##")
    if not na(primary_support)
        if key_levels_text != ""
            key_levels_text += "\n"
        key_levels_text += "支撐: " + str.tostring(primary_support, "#.##")
    if key_levels_text == ""
        key_levels_text := "計算中..."

    table.cell(explanation_table, 0, 2, "關鍵價位", bgcolor=header_bg_color, text_color=text_color_header, text_halign=text.align_center)
    table.cell(explanation_table, 1, 2, key_levels_text, bgcolor=content_bg_color, text_color=text_color_content, text_halign=text.align_left)

    // 計算距離關鍵位的百分比
    string distance_text = ""
    if not na(primary_resistance)
        distance_text += "↑" + str.tostring((primary_resistance - close) / close * 100, "#.#") + "%"
    if not na(primary_support)
        if distance_text != ""
            distance_text += "\n"
        distance_text += "↓" + str.tostring((close - primary_support) / close * 100, "#.#") + "%"
    if distance_text == ""
        distance_text := "N/A"

    table.cell(explanation_table, 2, 2, distance_text, bgcolor=accent_bg_color, text_color=text_color_accent, text_halign=text.align_center)

    // 第 3 行: 當前信號
    table.cell(explanation_table, 0, 3, "當前信號", bgcolor=header_bg_color, text_color=text_color_header, text_halign=text.align_center)
    table.cell(explanation_table, 1, 3, signal_text, bgcolor=content_bg_color, text_color=text_color_content, text_halign=text.align_left)
    table.merge_cells(explanation_table, 1, 3, 2, 3)

    // 第 4 行: 策略解說
    table.cell(explanation_table, 0, 4, "策略解說", bgcolor=header_bg_color, text_color=text_color_header, text_halign=text.align_center)
    table.cell(explanation_table, 1, 4, insight_text, bgcolor=content_bg_color, text_color=text_color_content, text_halign=text.align_left)
    table.merge_cells(explanation_table, 1, 4, 2, 4)

    // 第 5 行: 建議操作
    table.cell(explanation_table, 0, 5, "建議操作", bgcolor=header_bg_color, text_color=text_color_header, text_halign=text.align_center)
    table.cell(explanation_table, 1, 5, action_text, bgcolor=content_bg_color, text_color=text_color_content, text_halign=text.align_left)
    table.merge_cells(explanation_table, 1, 5, 2, 5)

    // 第 6 行: 風險管理
    table.cell(explanation_table, 0, 6, "風險管理", bgcolor=header_bg_color, text_color=text_color_header, text_halign=text.align_center)
    table.cell(explanation_table, 1, 6, "💡 每筆交易風險控制在總資本的1-2%以內\n🛡️ 嚴格執行止損，保護資本為首要任務\n📊 根據風險回報比調整倉位大小", bgcolor=content_bg_color, text_color=text_color_content, text_halign=text.align_left)
    table.merge_cells(explanation_table, 1, 6, 2, 6)
